{"version": "6.0", "nxVersion": "21.2.1", "pathMappings": {"@mobility-network/ui": ["packages/ui/src"], "@mobility-network/ui/*": ["packages/ui/src/*"], "@mobility-network/shared": ["packages/shared/src"], "@mobility-network/shared/*": ["packages/shared/src/*"]}, "nxJsonPlugins": [], "fileMap": {"projectFileMap": {"@mobility-network/marketing": [{"file": "apps/marketing/.gitignore", "hash": "1867017717006394117"}, {"file": "apps/marketing/README.md", "hash": "10766794746169900117"}, {"file": "apps/marketing/eslint.config.mjs", "hash": "14633139915648186571", "deps": ["npm:@eslint/eslintrc"]}, {"file": "apps/marketing/next.config.ts", "hash": "2615010867795100795", "deps": ["npm:next"]}, {"file": "apps/marketing/package.json", "hash": "10287746874406924137", "deps": ["npm:typescript", "npm:@types/node@20.19.1", "npm:@types/react", "npm:@types/react-dom", "npm:@tailwindcss/postcss", "npm:tailwindcss", "npm:eslint", "npm:eslint-config-next", "npm:@eslint/eslintrc", "@mobility-network/ui", "npm:react", "npm:react-dom", "npm:next"]}, {"file": "apps/marketing/postcss.config.mjs", "hash": "4766641093237987671"}, {"file": "apps/marketing/project.json", "hash": "13710669779408184942"}, {"file": "apps/marketing/public/file.svg", "hash": "15617830351863523835"}, {"file": "apps/marketing/public/globe.svg", "hash": "9134466864871335697"}, {"file": "apps/marketing/public/next.svg", "hash": "8300767200008744458"}, {"file": "apps/marketing/public/vercel.svg", "hash": "3007623926711696394"}, {"file": "apps/marketing/public/window.svg", "hash": "16677869292061012332"}, {"file": "apps/marketing/src/app/favicon.ico", "hash": "9157999678830540297"}, {"file": "apps/marketing/src/app/globals.css", "hash": "7980784277252421672"}, {"file": "apps/marketing/src/app/layout.tsx", "hash": "4622598555729013549", "deps": ["npm:next"]}, {"file": "apps/marketing/src/app/page.tsx", "hash": "11857874891651862620", "deps": ["npm:next"]}, {"file": "apps/marketing/tsconfig.json", "hash": "2587473170689616595"}], "@mobility-network/ui": [{"file": "packages/ui/package.json", "hash": "14240551753400405314", "deps": ["npm:@types/react", "npm:@types/react-dom", "npm:typescript", "npm:react", "npm:react-dom"]}, {"file": "packages/ui/src/Button.tsx", "hash": "4316814734833712178", "deps": ["npm:react"]}, {"file": "packages/ui/src/index.ts", "hash": "1548372080853166209"}, {"file": "packages/ui/tsconfig.json", "hash": "13599227607190410508"}], "@mobility-network/api": [{"file": "apps/api/index.ts", "hash": "13138597226221765836", "deps": ["npm:cors", "npm:express", "npm:helmet", "npm:morgan"]}, {"file": "apps/api/package.json", "hash": "3446542816690371444", "deps": ["npm:@types/cors", "npm:@types/express", "npm:@types/helmet", "npm:@types/morgan", "npm:@types/node@24.0.3", "npm:nodemon", "npm:ts-node", "npm:typescript", "npm:cors", "npm:express", "npm:helmet", "npm:morgan"]}, {"file": "apps/api/project.json", "hash": "177491770744326368"}, {"file": "apps/api/tsconfig.json", "hash": "5465089415256029786"}], "@mobility-network/dashboard": [{"file": "apps/dashboard/.gitignore", "hash": "13046175384202504187"}, {"file": "apps/dashboard/README.md", "hash": "13160460843517416105"}, {"file": "apps/dashboard/eslint.config.js", "hash": "13792119130412869889", "deps": ["npm:@eslint/js", "npm:eslint-plugin-react-hooks", "npm:eslint-plugin-react-refresh", "npm:globals", "npm:typescript-eslint"]}, {"file": "apps/dashboard/index.html", "hash": "4025202329384162906"}, {"file": "apps/dashboard/package.json", "hash": "7152253463247151661", "deps": ["npm:@eslint/js", "npm:@types/react", "npm:@types/react-dom", "npm:@vitejs/plugin-react", "npm:eslint", "npm:eslint-plugin-react-hooks", "npm:eslint-plugin-react-refresh", "npm:globals", "npm:typescript", "npm:typescript-eslint", "npm:vite", "@mobility-network/ui", "npm:@tanstack/react-query", "npm:axios", "npm:react", "npm:react-dom"]}, {"file": "apps/dashboard/public/vite.svg", "hash": "16046085400642193116"}, {"file": "apps/dashboard/src/App.css", "hash": "3739391659270974612"}, {"file": "apps/dashboard/src/App.tsx", "hash": "17982990676225942543", "deps": ["npm:react"]}, {"file": "apps/dashboard/src/assets/react.svg", "hash": "16069471864800376455"}, {"file": "apps/dashboard/src/index.css", "hash": "13504153635654440336"}, {"file": "apps/dashboard/src/main.tsx", "hash": "9699625239573703184", "deps": ["npm:react", "npm:react-dom"]}, {"file": "apps/dashboard/src/vite-env.d.ts", "hash": "12946363841406869089"}, {"file": "apps/dashboard/tsconfig.app.json", "hash": "13773565989543243116"}, {"file": "apps/dashboard/tsconfig.json", "hash": "2365188958523842680"}, {"file": "apps/dashboard/tsconfig.node.json", "hash": "3588689360788834997"}, {"file": "apps/dashboard/vite.config.ts", "hash": "14628939841492078449", "deps": ["npm:@vitejs/plugin-react", "npm:vite"]}]}, "nonProjectFiles": [{"file": ".czrc", "hash": "8557945762322343186"}, {"file": ".env.example", "hash": "10614449406725592023"}, {"file": ".eslintrc.js", "hash": "2361811425855846722"}, {"file": ".giti<PERSON>re", "hash": "6886958936990927842"}, {"file": ".husky/pre-commit", "hash": "17646163268225735670"}, {"file": ".lintstagedrc.js", "hash": "7726999271121848849"}, {"file": ".prettieri<PERSON>re", "hash": "59094935350196512"}, {"file": ".prettierrc.js", "hash": "15641616863915142149"}, {"file": ".vscode/extensions.json", "hash": "3206302592959402529"}, {"file": "README.md", "hash": "11197648671676730370"}, {"file": "nx.json", "hash": "5767515451063307043"}, {"file": "package.json", "hash": "15077166754619454443"}, {"file": "packages/shared/src/type.ts", "hash": "1541546793952677385"}, {"file": "packages/shared/tsconfig.json", "hash": "13599227607190410508"}, {"file": "pnpm-lock.yaml", "hash": "2235154436927694213"}, {"file": "pnpm-workspace.yaml", "hash": "2373171212127886927"}, {"file": "test.txt", "hash": "7147276431135252565"}, {"file": "tsconfig.json", "hash": "10099136544702164322"}]}}
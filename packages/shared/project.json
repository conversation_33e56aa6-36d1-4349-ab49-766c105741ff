{"name": "@mobility-network/shared", "sourceRoot": "packages/shared/src", "projectType": "library", "targets": {"dev": {"executor": "nx:run-commands", "options": {"command": "tsc --build --watch", "cwd": "packages/shared"}}, "build": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/dist"], "options": {"command": "tsc --build", "cwd": "packages/shared"}, "cache": true}, "lint": {"executor": "nx:run-commands", "options": {"command": "eslint src --ext ts", "cwd": "packages/shared"}}, "type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "packages/shared"}}}, "tags": ["scope:shared", "type:util"]}
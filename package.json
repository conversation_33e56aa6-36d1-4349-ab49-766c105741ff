{"name": "saas-mobility-network", "private": true, "version": "1.0.0", "description": "SaaS Mobility Network", "scripts": {"dev": "nx run-many --target=dev --all", "build": "nx run-many --target=build --all", "api:dev": "nx run @mobility-network/api:dev", "dashboard:dev": "nx run @mobility-network/dashboard:dev", "marketing:dev": "nx run @mobility-network/marketing:dev", "lint": "ESLINT_USE_FLAT_CONFIG=false eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "ESLINT_USE_FLAT_CONFIG=false eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "npx tsc --noEmit", "commit": "cz", "prepare": "husky"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "packageManager": "pnpm@10.12.1", "devDependencies": {"@nx/workspace": "^21.2.1", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "husky": "^9.1.7", "lint-staged": "^16.1.2", "nx": "21.2.1", "prettier": "^3.5.3", "typescript": "^5.8.3"}, "dependencies": {"@eslint/js": "^9.29.0", "eslint-import-resolver-typescript": "^4.4.3", "globals": "^16.2.0", "prettier-plugin-organize-imports": "^4.1.0", "typescript-eslint": "^8.34.1"}}
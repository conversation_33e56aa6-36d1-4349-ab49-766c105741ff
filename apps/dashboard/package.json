{"name": "@mobility-network/dashboard", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint src --ext ts,tsx", "lint:fix": "eslint src --ext ts,tsx --fix"}, "dependencies": {"@tanstack/react-query": "^5.80.10", "axios": "^1.10.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}
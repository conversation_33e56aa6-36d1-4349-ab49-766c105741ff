{"version": "6.0", "nxVersion": "21.2.1", "pathMappings": {"@mobility-network/ui": ["packages/ui/src"], "@mobility-network/ui/*": ["packages/ui/src/*"], "@mobility-network/shared": ["packages/shared/src"], "@mobility-network/shared/*": ["packages/shared/src/*"]}, "nxJsonPlugins": [], "fileMap": {"projectFileMap": {"@mobility-network/api": [{"file": "apps/api/index.ts", "hash": "3519273392699911358", "deps": ["npm:express", "npm:cors", "npm:helmet", "npm:morgan"]}, {"file": "apps/api/package.json", "hash": "2085718233783635581", "deps": ["npm:@types/cors", "npm:@types/express", "npm:@types/helmet", "npm:@types/morgan", "npm:@types/node", "npm:nodemon", "npm:ts-node", "npm:typescript", "npm:cors", "npm:express", "npm:helmet", "npm:morgan"]}, {"file": "apps/api/tsconfig.json", "hash": "7962720089281388003"}], "@mobility-network/dashboard": [{"file": "apps/dashboard/.gitignore", "hash": "13046175384202504187"}, {"file": "apps/dashboard/README.md", "hash": "13160460843517416105"}, {"file": "apps/dashboard/eslint.config.js", "hash": "16145672096969245511", "deps": ["npm:@eslint/js", "npm:globals@16.2.0", "npm:eslint-plugin-react-hooks", "npm:eslint-plugin-react-refresh", "npm:typescript-eslint"]}, {"file": "apps/dashboard/index.html", "hash": "4025202329384162906"}, {"file": "apps/dashboard/package.json", "hash": "14294110015511473712", "deps": ["npm:@eslint/js", "npm:@types/react", "npm:@types/react-dom", "npm:@vitejs/plugin-react", "npm:eslint", "npm:eslint-plugin-react-hooks", "npm:eslint-plugin-react-refresh", "npm:globals@16.2.0", "npm:typescript", "npm:typescript-eslint", "npm:vite", "npm:@tanstack/react-query", "npm:axios", "npm:react", "npm:react-dom"]}, {"file": "apps/dashboard/public/vite.svg", "hash": "16046085400642193116"}, {"file": "apps/dashboard/src/App.css", "hash": "3739391659270974612"}, {"file": "apps/dashboard/src/App.tsx", "hash": "9561001225065465909", "deps": ["npm:react"]}, {"file": "apps/dashboard/src/assets/react.svg", "hash": "16069471864800376455"}, {"file": "apps/dashboard/src/index.css", "hash": "13504153635654440336"}, {"file": "apps/dashboard/src/main.tsx", "hash": "11038211546439381838", "deps": ["npm:react", "npm:react-dom"]}, {"file": "apps/dashboard/src/vite-env.d.ts", "hash": "12946363841406869089"}, {"file": "apps/dashboard/tsconfig.app.json", "hash": "3992348939299782212"}, {"file": "apps/dashboard/tsconfig.json", "hash": "2365188958523842680"}, {"file": "apps/dashboard/tsconfig.node.json", "hash": "3588689360788834997"}, {"file": "apps/dashboard/vite.config.ts", "hash": "16225914092815047172", "deps": ["npm:vite", "npm:@vitejs/plugin-react"]}], "@mobility-network/ui": [{"file": "packages/ui/package.json", "hash": "16417798039885268088", "deps": ["npm:@types/react", "npm:@types/react-dom", "npm:typescript", "npm:react", "npm:react-dom"]}]}, "nonProjectFiles": [{"file": "nx.json", "hash": "18336426213955174907"}, {"file": "package.json", "hash": "13077644606563209762"}, {"file": "pnpm-lock.yaml", "hash": "7670328972176646452"}, {"file": "pnpm-workspace.yaml", "hash": "9750177317466281158"}]}}
{"apps/api": {"root": ["apps/api/project.json", "nx/core/project-json"], "name": ["apps/api/project.json", "nx/core/project-json"], "tags": ["apps/api/package.json", "nx/core/package-json"], "tags.npm:public": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["apps/api/package.json", "nx/core/package-json"], "metadata.description": ["apps/api/package.json", "nx/core/package-json"], "metadata.js": ["apps/api/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/api/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["apps/api/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/api/package.json", "nx/core/package-json"], "targets": ["apps/api/package.json", "nx/core/package-json"], "targets.start": ["apps/api/package.json", "nx/core/package-json"], "targets.start.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.start.options": ["apps/api/package.json", "nx/core/package-json"], "targets.start.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.start.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "targets.lint": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.options": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.options": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.options": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.options": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "sourceRoot": ["apps/api/project.json", "nx/core/project-json"], "targets.dev": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.executor": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.options": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.options.command": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.options.cwd": ["apps/api/project.json", "nx/core/project-json"], "targets.build": ["apps/api/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/api/project.json", "nx/core/project-json"], "targets.build.options": ["apps/api/project.json", "nx/core/project-json"], "targets.build.options.command": ["apps/api/project.json", "nx/core/project-json"], "targets.build.options.cwd": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.cache": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/dashboard": {"root": ["apps/dashboard/package.json", "nx/core/package-json"], "name": ["apps/dashboard/package.json", "nx/core/package-json"], "tags": ["apps/dashboard/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.js": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/dashboard/package.json", "nx/core/package-json"], "targets": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.dev": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.dev.executor": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.dev.options": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.dev.metadata": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.dev.options.script": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.dev.metadata.scriptContent": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.dev.metadata.runCommand": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.build": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.build.executor": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.build.options": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.build.metadata": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.build.options.script": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.build.metadata.scriptContent": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.build.metadata.runCommand": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.preview": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.preview.executor": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.preview.options": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.preview.metadata": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.preview.options.script": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.preview.metadata.scriptContent": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.preview.metadata.runCommand": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint.executor": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint.options": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint.metadata": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint.options.script": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.executor": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.options": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.metadata": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.options.script": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.metadata.scriptContent": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.metadata.runCommand": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.dev.cache": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/marketing": {"root": ["apps/marketing/project.json", "nx/core/project-json"], "name": ["apps/marketing/project.json", "nx/core/project-json"], "tags": ["apps/marketing/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.js": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/marketing/package.json", "nx/core/package-json"], "targets": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.executor": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.options": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.metadata": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.options.script": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.executor": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.options": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.metadata": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.options.script": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["apps/marketing/package.json", "nx/core/package-json"], "sourceRoot": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.executor": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.options": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.options.command": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.options.cwd": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.options": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.options.command": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.options.cwd": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.cache": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/ui": {"root": ["packages/ui/package.json", "nx/core/package-json"], "name": ["packages/ui/package.json", "nx/core/package-json"], "tags": ["packages/ui/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/ui/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ui/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/ui/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/ui/package.json", "nx/core/package-json"], "metadata.description": ["packages/ui/package.json", "nx/core/package-json"], "metadata.js": ["packages/ui/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ui/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ui/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ui/package.json", "nx/core/package-json"], "targets": ["packages/ui/package.json", "nx/core/package-json"], "targets.test": ["packages/ui/package.json", "nx/core/package-json"], "targets.test.executor": ["packages/ui/package.json", "nx/core/package-json"], "targets.test.options": ["packages/ui/package.json", "nx/core/package-json"], "targets.test.metadata": ["packages/ui/package.json", "nx/core/package-json"], "targets.test.options.script": ["packages/ui/package.json", "nx/core/package-json"], "targets.test.metadata.scriptContent": ["packages/ui/package.json", "nx/core/package-json"], "targets.test.metadata.runCommand": ["packages/ui/package.json", "nx/core/package-json"]}}
{"fileNames": ["../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.dom.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.d.ts", "../../node_modules/.pnpm/typescript@5.8.3/node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../node_modules/.pnpm/@types+react@19.1.8/node_modules/@types/react/global.d.ts", "../../node_modules/.pnpm/csstype@3.1.3/node_modules/csstype/index.d.ts", "../../node_modules/.pnpm/@types+react@19.1.8/node_modules/@types/react/index.d.ts", "../../node_modules/.pnpm/@types+react@19.1.8/node_modules/@types/react/jsx-runtime.d.ts", "./src/button.tsx", "./src/index.ts", "../../node_modules/.pnpm/@types+react-dom@19.1.6_@types+react@19.1.8/node_modules/@types/react-dom/index.d.ts"], "fileIdsList": [[18], [16, 17], [18, 19], [19, 20]], "fileInfos": [{"version": "69684132aeb9b5642cbcd9e22dff7818ff0ee1aa831728af0ecf97d3364d5546", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "092c2bfe125ce69dbb1223c85d68d4d2397d7d8411867b5cc03cec902c233763", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "07f073f19d67f74d732b1adea08e1dc66b1b58d77cb5b43931dee3d798a2fd53", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "936e80ad36a2ee83fc3caf008e7c4c5afe45b3cf3d5c24408f039c1d47bdc1df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "170d4db14678c68178ee8a3d5a990d5afb759ecb6ec44dbd885c50f6da6204f6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "d4d7d3f832882a4b2d611a7eaaa80c780c3342b5732090130fa9af4a40bd051e", "impliedFormat": 1}, {"version": "42c169fb8c2d42f4f668c624a9a11e719d5d07dacbebb63cbcf7ef365b0a75b3", "impliedFormat": 1}, {"version": "cfe46d3cde4d560afa8a669175ce7a3c0bc4cbb6e4f492fb0cc0c9cc11357b46", "signature": "4efd124e6215f8175f73eeb9349518e00fbcf9fb9407fd57301c1110fedd04bb"}, "ee083b355085d100be23d1decf29d53d4ddefdda6ae215abc7cbbf131735ceef", {"version": "a0acca63c9e39580f32a10945df231815f0fe554c074da96ba6564010ffbd2d8", "impliedFormat": 1}], "root": [20, 21], "options": {"allowJs": true, "allowSyntheticDefaultImports": true, "composite": true, "declaration": true, "declarationMap": true, "esModuleInterop": true, "jsx": 4, "module": 99, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "strict": true, "target": 7}, "referencedMap": [[22, 1], [18, 2], [19, 1], [20, 3], [21, 4]], "latestChangedDtsFile": "./dist/index.d.ts", "version": "5.8.3"}
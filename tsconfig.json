{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"@partlytic/ui": ["packages/ui/src"], "@partlytic/ui/*": ["packages/ui/src/*"], "@partlytic/shared": ["packages/shared/src"], "@partlytic/shared/*": ["packages/shared/src/*"]}}, "include": ["apps/*/src/**/*", "packages/*/src/**/*"], "exclude": ["node_modules", "dist", ".next"]}
# 🏗️ SaaS Mobility Network - Monorepo Architecture

## **📋 Overview**

This is a modern, enterprise-grade monorepo built for a SaaS mobility network platform using **Nx**, **pnpm workspaces**, and **TypeScript**. The architecture follows domain-driven design principles with clear separation of concerns and optimized dependency management.

## **🎯 Architecture Principles**

- **Monorepo Strategy**: Single repository with multiple applications and shared packages
- **Dependency Inversion**: Shared packages provide common functionality to applications
- **Build Orchestration**: Nx manages build order and caching for optimal performance
- **Type Safety**: Full TypeScript coverage across all packages and applications
- **Code Sharing**: Reusable components and types reduce duplication

## **📁 Project Structure**

```
saas-mobility-network/
├── apps/                           # Applications
│   ├── dashboard/                  # React + Vite Admin Dashboard
│   │   ├── src/
│   │   ├── package.json
│   │   ├── project.json           # Nx configuration
│   │   ├── tsconfig.json
│   │   └── vite.config.ts
│   ├── marketing/                  # Next.js Marketing Website
│   │   ├── src/
│   │   ├── package.json
│   │   ├── project.json
│   │   ├── tsconfig.json
│   │   └── next.config.ts
│   └── api/                        # Node.js + Express API
│       ├── index.ts
│       ├── package.json
│       ├── project.json
│       └── tsconfig.json
├── packages/                       # Shared Libraries
│   ├── ui/                         # React Component Library
│   │   ├── src/
│   │   │   ├── Button.tsx
│   │   │   └── index.ts
│   │   ├── package.json
│   │   ├── project.json
│   │   └── tsconfig.json
│   └── shared/                     # Common Types & Utilities
│       ├── src/
│       │   ├── type.ts
│       │   └── index.ts
│       ├── package.json
│       ├── project.json
│       └── tsconfig.json
├── .husky/                         # Git Hooks
├── .nx/                           # Nx Cache & Metadata
├── node_modules/                   # Dependencies
├── docs/                          # Documentation
├── nx.json                        # Nx Workspace Configuration
├── pnpm-workspace.yaml            # pnpm Workspace Configuration
├── tsconfig.json                  # Root TypeScript Configuration
├── package.json                   # Root Package Configuration
├── .gitignore
├── .prettierrc
├── .lintstagedrc.js
└── README.md
```

## **🏛️ Application Architecture**

### **Dashboard App** (`apps/dashboard`)
- **Technology**: React 19 + Vite + TypeScript
- **Purpose**: Admin dashboard for mobility network management
- **Port**: 3000
- **Dependencies**: `@mobility-network/ui`, `@mobility-network/shared`
- **Build Output**: Static files for deployment

### **Marketing App** (`apps/marketing`)
- **Technology**: Next.js 15 + TypeScript + Tailwind CSS
- **Purpose**: Public marketing website
- **Port**: 3002
- **Dependencies**: `@mobility-network/ui`, `@mobility-network/shared`
- **Build Output**: Static/SSR pages for deployment

### **API** (`apps/api`)
- **Technology**: Node.js + Express + TypeScript
- **Purpose**: Backend API for mobility network services
- **Port**: 3001
- **Dependencies**: `@mobility-network/shared`
- **Build Output**: Compiled JavaScript for server deployment

## **📦 Shared Package Architecture**

### **UI Package** (`packages/ui`)
```typescript
// Package Scope: Frontend applications only
// Purpose: Reusable React components with consistent styling

export interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
}

export const Button: React.FC<ButtonProps> = ({ ... });
```

### **Shared Package** (`packages/shared`)
```typescript
// Package Scope: All applications (frontend + backend)
// Purpose: Common types, interfaces, and utilities

export interface User {
  id: number;
  email: string;
  name: string;
  createdAt: Date;
  updatedAt?: Date;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}
```

## **🔗 Dependency Graph**

```mermaid
graph TD
    A[Dashboard App] --> B[UI Package]
    A --> C[Shared Package]
    D[Marketing App] --> B
    D --> C
    E[API] --> C
    
    B --> F[React Dependencies]
    C --> G[TypeScript]
    
    H[Root Workspace] --> A
    H --> D
    H --> E
    H --> B
    H --> C
```

## **⚙️ Build System Architecture**

### **Nx Configuration** (`nx.json`)
```json
{
  "targetDefaults": {
    "build": {
      "cache": true,
      "dependsOn": ["^build"]
    },
    "dev": {
      "cache": false
    }
  }
}
```

### **Build Dependencies**
- **Dashboard**: `ui:build` → `shared:build` → `dashboard:build`
- **Marketing**: `ui:build` → `shared:build` → `marketing:build`
- **API**: `shared:build` → `api:build`

### **Caching Strategy**
- **Build artifacts**: Cached by Nx for faster subsequent builds
- **Shared packages**: Cached when unchanged (20ms build time)
- **Applications**: Rebuild only when dependencies change

## **🔧 Technology Stack**

| Layer | Technology | Purpose |
|-------|------------|---------|
| **Monorepo** | Nx + pnpm | Build orchestration & package management |
| **Frontend** | React 19 + Vite | Modern React development |
| **SSR/SSG** | Next.js 15 | Marketing site with SEO optimization |
| **Backend** | Node.js + Express | RESTful API services |
| **Language** | TypeScript | Type safety across all packages |
| **Styling** | Tailwind CSS | Utility-first CSS framework |
| **Code Quality** | ESLint + Prettier | Linting and formatting |
| **Git Hooks** | Husky + lint-staged | Pre-commit validation |

## **🚀 Deployment Architecture**

### **Independent Deployments**
Each application can be deployed independently:

- **Dashboard**: Static hosting (Vercel, Netlify, S3)
- **Marketing**: Vercel/Netlify with SSR support
- **API**: Container deployment (Docker, Kubernetes)

### **Shared Package Distribution**
- Built as npm packages within workspace
- Consumed via workspace protocol (`workspace:*`)
- Automatic linking during development

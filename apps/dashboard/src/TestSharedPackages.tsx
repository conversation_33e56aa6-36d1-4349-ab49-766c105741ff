import type { ApiResponse, User } from '@mobility-network/shared';
import { Button } from '@mobility-network/ui';
import React from 'react';

// Test data using shared types
const testUser: User = {
  id: 1,
  email: '<EMAIL>',
  name: 'Test User',
  createdAt: new Date(),
  updatedAt: new Date(), // Using the new field we added
};

const testResponse: ApiResponse<User> = {
  data: testUser,
  success: true,
  message: 'User data loaded successfully',
};

export const TestSharedPackages: React.FC = () => {
  const handlePrimaryClick = () => {
    console.log('Primary button clicked!', testResponse);
  };

  const handleSecondaryClick = () => {
    console.log('Secondary button clicked!', testUser);
  };

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>🧪 Shared Packages Test</h2>

      <div style={{ marginBottom: '20px' }}>
        <h3>📦 @mobility-network/ui Components:</h3>
        <div style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}>
          <Button variant="primary" onClick={handlePrimaryClick}>
            Primary Button
          </Button>
          <Button variant="secondary" onClick={handleSecondaryClick}>
            Secondary Button
          </Button>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>🔧 @mobility-network/shared Types:</h3>
        <div
          style={{
            backgroundColor: '#f5f5f5',
            padding: '15px',
            borderRadius: '5px',
          }}
        >
          <h4>User Data:</h4>
          <pre style={{ margin: 0, fontSize: '12px' }}>
            {JSON.stringify(testUser, null, 2)}
          </pre>
        </div>

        <div
          style={{
            backgroundColor: '#f0f8ff',
            padding: '15px',
            borderRadius: '5px',
            marginTop: '10px',
          }}
        >
          <h4>API Response:</h4>
          <pre style={{ margin: 0, fontSize: '12px' }}>
            {JSON.stringify(testResponse, null, 2)}
          </pre>
        </div>
      </div>

      <div
        style={{
          backgroundColor: '#e8f5e8',
          padding: '15px',
          borderRadius: '5px',
        }}
      >
        <h3>✅ Dependency Management Test Results:</h3>
        <ul>
          <li>✅ @mobility-network/ui imports working correctly</li>
          <li>✅ @mobility-network/shared types working correctly</li>
          <li>✅ TypeScript compilation successful</li>
          <li>✅ Automatic dependency building functional</li>
        </ul>
      </div>
    </div>
  );
};

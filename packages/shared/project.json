{"name": "@partlytic/shared", "sourceRoot": "packages/shared/src", "projectType": "library", "targets": {"dev": {"executor": "nx:run-commands", "options": {"command": "tsc --build --watch", "cwd": "packages/shared"}}, "build": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/dist"], "options": {"command": "tsc --build", "cwd": "packages/shared"}, "cache": true}}, "tags": ["scope:shared", "type:util"]}
# 👨‍💻 Senior Developer Guide - Running & Testing

## **🚀 Quick Start for Senior Developers**

### **Prerequisites**
```bash
# Required versions
node --version    # >= 18.0.0
pnpm --version    # >= 8.0.0
```

### **Initial Setup**
```bash
# Clone and setup
git clone <repository-url>
cd saas-mobility-network
pnpm install

# Verify installation
pnpm run build    # Should complete without errors
```

## **🔧 Development Workflows**

### **1. Full Stack Development**
```bash
# Start all applications with dependency management
pnpm run dev

# This executes:
# 1. Build shared packages first
# 2. Start shared packages in watch mode
# 3. Start all apps in parallel
```

**Expected Output:**
```
✔ @mobility-network/shared:build (20ms - cached)
✔ @mobility-network/ui:build (20ms - cached)

[0] @mobility-network/shared:dev (watching...)
[0] @mobility-network/ui:dev (watching...)
[1] @mobility-network/api:dev (http://localhost:3001)
[1] @mobility-network/dashboard:dev (http://localhost:3000)
[1] @mobility-network/marketing:dev (http://localhost:3002)
```

### **2. Individual Application Development**
```bash
# API only (with auto-dependency building)
pnpm run api:dev
# → Builds @mobility-network/shared first
# → Starts API on port 3001

# Dashboard only (with auto-dependency building)
pnpm run dashboard:dev
# → Builds @mobility-network/ui + @mobility-network/shared first
# → Starts dashboard on port 3000

# Marketing only (with auto-dependency building)
pnpm run marketing:dev
# → Builds @mobility-network/ui + @mobility-network/shared first
# → Starts marketing on port 3002
```

### **3. Shared Package Development**
```bash
# Build shared packages only
pnpm run build:shared

# Watch mode for shared packages
pnpm run dev:shared
```

## **🧪 Testing Strategies**

### **1. Build Verification**
```bash
# Test complete build pipeline
pnpm run build

# Expected: All 5 projects build successfully
# ✔ @mobility-network/shared:build
# ✔ @mobility-network/ui:build  
# ✔ @mobility-network/api:build
# ✔ @mobility-network/dashboard:build
# ✔ @mobility-network/marketing:build
```

### **2. Dependency Testing**
```bash
# Test dependency graph
nx graph
# Opens visual dependency graph in browser

# Test affected builds (after making changes)
nx affected:build
# Only rebuilds changed packages and dependents
```

### **3. Type Safety Testing**
```bash
# TypeScript compilation check
nx run-many --target=type-check --all

# Individual package type checking
cd packages/ui && pnpm type-check
cd packages/shared && pnpm type-check
```

### **4. Endpoint Testing**
```bash
# Start all services
pnpm run dev

# Test API endpoints
curl http://localhost:3001/health
# Expected: {"status":"OK","timestamp":"..."}

# Test frontend applications
curl -I http://localhost:3000  # Dashboard
curl -I http://localhost:3002  # Marketing
# Expected: HTTP/1.1 200 OK
```

### **5. Hot Reload Testing**
```bash
# 1. Start dev environment
pnpm run dev

# 2. Make changes to shared packages
echo "export const TEST_CONSTANT = 'test';" >> packages/shared/src/index.ts

# 3. Verify auto-rebuild
# Expected: Shared package rebuilds, apps reload automatically
```

## **🔍 Advanced Development Commands**

### **Nx-Specific Commands**
```bash
# Show all projects
nx show projects

# Run specific target for all projects
nx run-many --target=build --all

# Run target for specific projects
nx run-many --target=dev --projects=@mobility-network/api,@mobility-network/dashboard

# Show project details
nx show project @mobility-network/dashboard

# Clear Nx cache
nx reset
```

### **Dependency Analysis**
```bash
# Show project dependencies
nx graph --focus=@mobility-network/dashboard

# Show what's affected by changes
nx affected:graph

# List affected projects
nx affected:apps
nx affected:libs
```

### **Performance Optimization**
```bash
# Build with detailed timing
nx run-many --target=build --all --verbose

# Parallel execution control
nx run-many --target=build --all --parallel=3

# Cache analysis
nx run-many --target=build --all --skip-nx-cache
```

## **🐛 Debugging & Troubleshooting**

### **Common Issues & Solutions**

#### **1. Port Conflicts**
```bash
# Check port usage
lsof -i :3000 -i :3001 -i :3002

# Kill processes on specific ports
lsof -ti:3000,3001,3002 | xargs kill -9

# Restart clean
pnpm run dev
```

#### **2. Build Failures**
```bash
# Clean all build artifacts
rm -rf packages/*/dist apps/*/dist apps/*/.next

# Clear Nx cache
nx reset

# Rebuild from scratch
pnpm run build
```

#### **3. Dependency Issues**
```bash
# Reinstall dependencies
rm -rf node_modules packages/*/node_modules apps/*/node_modules
pnpm install

# Verify workspace linking
pnpm list --depth=0
```

#### **4. TypeScript Errors**
```bash
# Clean TypeScript build info
find . -name "*.tsbuildinfo" -delete

# Rebuild TypeScript projects
nx run-many --target=build --projects=@mobility-network/shared,@mobility-network/ui
```

## **📊 Performance Monitoring**

### **Build Performance**
```bash
# Measure build times
time pnpm run build

# Nx build analysis
nx run-many --target=build --all --verbose
```

### **Development Performance**
```bash
# Monitor file changes
nx run-many --target=dev --all --verbose

# Check cache hit rates
nx report
```

## **🔒 Code Quality Assurance**

### **Pre-commit Validation**
```bash
# Manual pre-commit check
pnpm run lint
pnpm run format:check
pnpm run type-check

# Fix issues automatically
pnpm run lint:fix
pnpm run format
```

### **Continuous Integration Simulation**
```bash
# Simulate CI pipeline locally
pnpm install --frozen-lockfile
pnpm run build
pnpm run lint
pnpm run type-check
```

## **🚀 Production Readiness**

### **Build for Production**
```bash
# Production builds
pnpm run build

# Verify build outputs
ls -la apps/dashboard/dist
ls -la apps/marketing/.next
ls -la apps/api/dist
```

### **Environment Configuration**
```bash
# Copy environment template
cp .env.example .env

# Configure for production
NODE_ENV=production pnpm run build
```

# SaaS Mobility Network - Monorepo

A modern, scalable monorepo setup for a SaaS mobility network platform built with React, Next.js, Node.js, and TypeScript.

## 🏗️ Architecture

This monorepo contains:

- **Dashboard App** (`apps/dashboard`) - React 18 + Vite admin dashboard
- **Marketing App** (`apps/marketing`) - Next.js 14 marketing website
- **API** (`apps/api`) - Node.js + Express + TypeScript backend
- **Shared UI** (`packages/ui`) - Reusable React components
- **Shared Types** (`packages/shared`) - Common TypeScript types

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- pnpm 8+

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd saas-mobility-network

# Install dependencies
pnpm install

# Start all applications
pnpm run dev
```

### Individual App Development

```bash
# Dashboard (React + Vite) - http://localhost:3000
pnpm run dashboard:dev

# API (Node.js + Express) - http://localhost:3001
pnpm run api:dev

# Marketing (Next.js) - http://localhost:3002
pnpm run marketing:dev
```

## 📦 Package Management

This project uses **pnpm workspaces** for efficient dependency management:

- Shared dependencies are hoisted to the root
- Each app maintains its own specific dependencies
- Automatic linking between workspace packages

## 🛠️ Development Tools

### Code Quality
- **ESLint** - Linting with React, TypeScript, and accessibility rules
- **Prettier** - Code formatting with import organization
- **Husky** - Git hooks for pre-commit validation
- **lint-staged** - Run linters on staged files only

### Build & Development
- **Nx** - Build orchestration and caching
- **TypeScript** - Type safety across all packages
- **Vite** - Fast development for dashboard
- **Next.js** - Full-stack framework for marketing

## 🔧 Available Scripts

```bash
# Development
pnpm run dev              # Start all apps
pnpm run dashboard:dev    # Dashboard only
pnpm run api:dev         # API only
pnpm run marketing:dev   # Marketing only

# Building
pnpm run build           # Build all apps
pnpm run type-check      # TypeScript validation

# Code Quality
pnpm run lint            # Lint all files
pnpm run lint:fix        # Fix linting issues
pnpm run format          # Format all files
pnpm run format:check    # Check formatting

# Git
pnpm run commit          # Conventional commits with Commitizen
```

## 📁 Project Structure

```
├── apps/
│   ├── dashboard/       # React + Vite dashboard
│   ├── marketing/       # Next.js marketing site
│   └── api/            # Node.js + Express API
├── packages/
│   ├── ui/             # Shared React components
│   └── shared/         # Shared TypeScript types
├── .husky/             # Git hooks
├── nx.json             # Nx configuration
├── pnpm-workspace.yaml # pnpm workspace config
└── tsconfig.json       # Root TypeScript config
```

## 🔗 Shared Packages

### @mobility-network/ui
Reusable React components with TypeScript support.

```tsx
import { Button } from '@mobility-network/ui';

<Button variant="primary" onClick={handleClick}>
  Click me
</Button>
```

### @mobility-network/shared
Common TypeScript types and interfaces.

```tsx
import type { User, ApiResponse } from '@mobility-network/shared';
```

## 🚦 API Endpoints

The API server runs on `http://localhost:3001` with the following endpoints:

- `GET /health` - Health check endpoint

## 🎯 Production Deployment

Each app can be built and deployed independently:

```bash
# Build specific app
nx run @mobility-network/dashboard:build
nx run @mobility-network/marketing:build
nx run @mobility-network/api:build

# Or build all
pnpm run build
```

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env` and configure:

```bash
# Database
DATABASE_URL=**********************************************************

# API
PORT=3001
NODE_ENV=development

# Next.js
NEXT_PUBLIC_API_URL=http://localhost:3001
```

### TypeScript Path Mapping
Shared packages are automatically mapped in `tsconfig.json`:

```json
{
  "paths": {
    "@mobility-network/ui": ["packages/ui/src"],
    "@mobility-network/shared": ["packages/shared/src"]
  }
}
```

## 📚 Documentation

### **Architecture & Setup**
- **[Architecture Overview](docs/ARCHITECTURE.md)** - Complete monorepo architecture documentation
- **[Architecture Setup](docs/ARCHITECTURE_SETUP.md)** - Detailed explanation of architectural decisions
- **[Senior Developer Guide](docs/SENIOR_DEVELOPER_GUIDE.md)** - Advanced usage and testing strategies
- **[Development Workflows](docs/DEVELOPMENT_WORKFLOWS.md)** - Complete development, testing, and deployment processes
- **[Git Commit Rules](docs/GIT_COMMIT_RULES.md)** - Conventional commits and Git workflow guidelines

### **Quick Reference**
- **Applications**: Dashboard (3000), API (3001), Marketing (3002)
- **Shared Packages**: `@mobility-network/ui` (frontend), `@mobility-network/shared` (universal)
- **Key Commands**: `pnpm run dev`, `pnpm run build`, `nx affected:build`

## 🎯 Enterprise Features

✅ **Intelligent Build System** - Nx orchestration with caching
✅ **Automatic Dependency Management** - Shared packages build first
✅ **Hot Reload Across Packages** - Changes propagate instantly
✅ **Type Safety** - Full TypeScript coverage with cross-package validation
✅ **Code Quality** - ESLint, Prettier, Husky integration
✅ **Performance Optimized** - 80% faster builds with intelligent caching

## 🤝 Contributing

1. Create a feature branch
2. Make your changes
3. Run `pnpm run commit` for conventional commits
4. Push and create a pull request

Pre-commit hooks will automatically:
- Lint and format your code
- Run type checking
- Validate commit messages

## 📄 License

ISC License - see LICENSE file for details.

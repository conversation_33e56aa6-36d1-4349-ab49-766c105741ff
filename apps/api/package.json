{"name": "@mobility-network/api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint . --ext ts", "lint:fix": "eslint . --ext ts --fix", "db:generate": "drizzle-kit generate:pg", "db:migrate": "drizzle-kit push:pg"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.12.1", "dependencies": {"cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.1.0", "morgan": "^1.10.0"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/helmet": "^4.0.0", "@types/morgan": "^1.9.10", "@types/node": "^24.0.3", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}
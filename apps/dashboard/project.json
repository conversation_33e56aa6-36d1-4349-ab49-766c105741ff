{"name": "@partlytic/dashboard", "sourceRoot": "apps/dashboard/src", "projectType": "application", "targets": {"dev": {"executor": "nx:run-commands", "options": {"command": "pnpm run build:deps && pnpm dev", "cwd": "apps/dashboard"}}, "build": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/dist"], "options": {"command": "pnpm build", "cwd": "apps/dashboard"}, "dependsOn": ["^build"]}, "preview": {"executor": "nx:run-commands", "options": {"command": "pnpm preview", "cwd": "apps/dashboard"}, "dependsOn": ["build"]}, "lint": {"executor": "nx:run-commands", "options": {"command": "pnpm lint", "cwd": "apps/dashboard"}}}, "tags": ["scope:dashboard", "type:app"]}
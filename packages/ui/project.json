{"name": "@mobility-network/ui", "sourceRoot": "packages/ui/src", "projectType": "library", "targets": {"build": {"executor": "nx:run-commands", "outputs": ["{projectRoot}/dist"], "options": {"command": "tsc --build", "cwd": "packages/ui"}, "cache": true}, "lint": {"executor": "nx:run-commands", "options": {"command": "eslint src --ext ts,tsx", "cwd": "packages/ui"}}, "type-check": {"executor": "nx:run-commands", "options": {"command": "tsc --noEmit", "cwd": "packages/ui"}}}, "tags": ["scope:shared", "type:ui"]}
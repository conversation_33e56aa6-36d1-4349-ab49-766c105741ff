import type { ApiResponse, User } from '@partlytic/shared';
import { Button } from '@partlytic/ui';
import React from 'react';

// Test data using shared types
const testUser: User = {
  id: 1,
  email: '<EMAIL>',
  name: 'Test User',
  createdAt: new Date(),
  updatedAt: new Date(), // Using the new field we added
};

const testResponse: ApiResponse<User> = {
  data: testUser,
  success: true,
  message: 'User data loaded successfully',
};

const containerStyle = {
  padding: '20px',
  fontFamily: 'Arial, sans-serif',
};

const sectionStyle = {
  marginBottom: '20px',
};

const buttonContainerStyle = {
  display: 'flex',
  gap: '10px',
  marginBottom: '10px',
};

const codeBlockStyle = {
  padding: '15px',
  borderRadius: '5px',
  marginTop: '10px',
};

const preStyle = {
  margin: 0,
  fontSize: '12px',
};

export const TestSharedPackages: React.FC = () => {
  const handlePrimaryClick = () => {
    console.log('Primary button clicked!', testResponse);
  };

  const handleSecondaryClick = () => {
    console.log('Secondary button clicked!', testUser);
  };

  const handleDangerClick = () => {
    console.log('Danger button clicked!');
  };

  return (
    <div style={containerStyle}>
      <h2>🧪 Shared Packages Test</h2>

      <div style={sectionStyle}>
        <h3>📦 @partlytic/ui Components:</h3>
        <div style={buttonContainerStyle}>
          <Button variant="primary" onClick={handlePrimaryClick}>
            Primary Button
          </Button>
          <Button variant="secondary" onClick={handleSecondaryClick}>
            Secondary Button
          </Button>
          <Button variant="danger" onClick={handleDangerClick}>
            Danger Button
          </Button>
          <Button variant="primary" disabled>
            Disabled Button
          </Button>
        </div>
      </div>

      <div style={sectionStyle}>
        <h3>🔧 @partlytic/shared Types:</h3>
        <div style={{ ...codeBlockStyle, backgroundColor: '#f5f5f5' }}>
          <h4>User Data:</h4>
          <pre style={preStyle}>{JSON.stringify(testUser, null, 2)}</pre>
        </div>

        <div style={{ ...codeBlockStyle, backgroundColor: '#f0f8ff' }}>
          <h4>API Response:</h4>
          <pre style={preStyle}>{JSON.stringify(testResponse, null, 2)}</pre>
        </div>
      </div>

      <div style={{ ...codeBlockStyle, backgroundColor: '#e8f5e8' }}>
        <h3>✅ Dependency Management Test Results:</h3>
        <ul>
          <li>✅ @partlytic/ui imports working correctly</li>
          <li>✅ @partlytic/shared types working correctly</li>
          <li>✅ TypeScript compilation successful</li>
          <li>✅ Automatic dependency building functional</li>
          <li>
            ✅ All button variants working (primary, secondary, danger,
            disabled)
          </li>
        </ul>
      </div>
    </div>
  );
};

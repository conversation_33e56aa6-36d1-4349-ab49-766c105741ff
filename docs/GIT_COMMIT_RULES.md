# 📝 Git Commit Rules & Flow

## **🎯 Commit Message Convention**

This project follows **Conventional Commits** specification for consistent, semantic commit messages.

### **📋 Commit Message Format**

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

### **🏷️ Commit Types**

| Type | Description | Example |
|------|-------------|---------|
| `feat` | New feature | `feat: add user authentication` |
| `fix` | Bug fix | `fix: resolve API timeout issue` |
| `docs` | Documentation changes | `docs: update API documentation` |
| `style` | Code style changes (formatting, etc.) | `style: fix indentation in components` |
| `refactor` | Code refactoring | `refactor: extract shared utility functions` |
| `perf` | Performance improvements | `perf: optimize database queries` |
| `test` | Adding or updating tests | `test: add unit tests for user service` |
| `build` | Build system changes | `build: update webpack configuration` |
| `ci` | CI/CD changes | `ci: add GitHub Actions workflow` |
| `chore` | Maintenance tasks | `chore: update dependencies` |
| `revert` | Revert previous commit | `revert: revert "feat: add user auth"` |

### **🎯 Scope Examples**

| Scope | Description | Example |
|-------|-------------|---------|
| `api` | Backend API changes | `feat(api): add user endpoints` |
| `dashboard` | Dashboard app changes | `fix(dashboard): resolve login form` |
| `marketing` | Marketing app changes | `feat(marketing): add landing page` |
| `ui` | UI package changes | `feat(ui): add Button component` |
| `shared` | Shared package changes | `feat(shared): add User type` |
| `deps` | Dependency changes | `chore(deps): update React to v19` |

## **✅ Good Commit Examples**

```bash
# Feature commits
feat: implement user authentication system
feat(api): add JWT token validation
feat(dashboard): add user management interface

# Bug fixes
fix: resolve memory leak in data fetching
fix(ui): correct Button component styling
fix(api): handle null user data gracefully

# Documentation
docs: add architecture documentation
docs(api): update endpoint documentation
docs: improve README installation steps

# Refactoring
refactor: extract shared validation logic
refactor(dashboard): simplify component structure
refactor: move types to shared package

# Performance
perf: optimize bundle size with code splitting
perf(api): add database query caching
perf: reduce initial page load time

# Multi-line commits
feat: implement complete user management system

- Add user registration and login
- Implement JWT authentication
- Add user profile management
- Include role-based access control

Closes #123
```

## **❌ Bad Commit Examples**

```bash
# Too vague
fix: bug fix
feat: new stuff
update: changes

# Not descriptive
fix: oops
feat: add thing
docs: update

# Wrong type
feat: fix typo (should be 'fix')
fix: add new feature (should be 'feat')

# Missing description
feat:
fix(api):
```

## **🔄 Git Workflow**

### **1. Feature Development Flow**

```bash
# 1. Create feature branch
git checkout -b feature/user-authentication

# 2. Make changes and commit frequently
git add .
git commit -m "feat: add login form component"

git add .
git commit -m "feat: implement JWT authentication"

git add .
git commit -m "test: add authentication tests"

# 3. Push feature branch
git push origin feature/user-authentication

# 4. Create Pull Request
# 5. Code review and merge
```

### **2. Hotfix Flow**

```bash
# 1. Create hotfix branch from main
git checkout main
git checkout -b hotfix/critical-security-fix

# 2. Make fix and commit
git add .
git commit -m "fix: resolve critical security vulnerability"

# 3. Push and create urgent PR
git push origin hotfix/critical-security-fix
```

### **3. Release Flow**

```bash
# 1. Create release branch
git checkout -b release/v1.2.0

# 2. Update version and changelog
git add .
git commit -m "chore: bump version to 1.2.0"

# 3. Merge to main and tag
git checkout main
git merge release/v1.2.0
git tag -a v1.2.0 -m "Release version 1.2.0"
git push origin main --tags
```

## **🛠️ Pre-commit Hooks**

### **Automatic Checks**

Our pre-commit hooks automatically run:

```bash
# 1. ESLint - Code linting
eslint --fix

# 2. Prettier - Code formatting
prettier --write

# 3. Type checking
tsc --noEmit

# 4. Lint staged files only
lint-staged
```

### **Bypassing Hooks (Emergency Only)**

```bash
# Skip pre-commit hooks (use sparingly)
git commit --no-verify -m "fix: emergency production fix"
```

## **📊 Commit Best Practices**

### **✅ Do's**

- **Write clear, descriptive messages**
- **Use present tense** ("add feature" not "added feature")
- **Keep first line under 50 characters**
- **Use body for detailed explanation**
- **Reference issues/tickets** when applicable
- **Make atomic commits** (one logical change per commit)
- **Test before committing**

### **❌ Don'ts**

- **Don't commit broken code**
- **Don't mix unrelated changes**
- **Don't commit generated files** (dist/, node_modules/)
- **Don't use vague messages**
- **Don't commit secrets or sensitive data**
- **Don't force push to main branch**

## **🔧 Commit Tools**

### **Commitizen (Recommended)**

```bash
# Install globally
npm install -g commitizen

# Use interactive commit
pnpm run commit

# Or use directly
git cz
```

### **Manual Commit Template**

```bash
# Set commit template
git config commit.template .gitmessage

# Create template file
echo "
# <type>[optional scope]: <description>
#
# [optional body]
#
# [optional footer(s)]
" > .gitmessage
```

## **📈 Commit Frequency Guidelines**

### **Development Phase**
- **Commit often** - Every logical change
- **Small commits** - Easy to review and revert
- **Work-in-progress** - Use `wip:` prefix for incomplete work

### **Review Phase**
- **Squash related commits** before merging
- **Clean up commit history**
- **Ensure each commit builds successfully**

## **🔍 Commit Review Checklist**

Before committing, verify:

- [ ] **Message follows convention**
- [ ] **Code builds successfully**
- [ ] **Tests pass**
- [ ] **No sensitive data included**
- [ ] **Generated files excluded**
- [ ] **Related files included**
- [ ] **Logical grouping of changes**

## **🚀 Advanced Git Commands**

### **Interactive Rebase**
```bash
# Clean up last 3 commits
git rebase -i HEAD~3

# Squash, edit, or reorder commits
```

### **Amend Last Commit**
```bash
# Fix last commit message
git commit --amend -m "corrected message"

# Add files to last commit
git add forgotten-file.js
git commit --amend --no-edit
```

### **Cherry Pick**
```bash
# Apply specific commit to current branch
git cherry-pick <commit-hash>
```

## **📋 Troubleshooting**

### **Common Issues**

1. **Pre-commit hooks failing**
   ```bash
   # Fix linting issues
   pnpm run lint:fix

   # Format code
   pnpm run format

   # Then retry commit
   git commit -m "your message"
   ```

2. **Merge conflicts**
   ```bash
   # Resolve conflicts manually
   git add resolved-files
   git commit -m "resolve merge conflicts"
   ```

3. **Wrong commit message**
   ```bash
   # Amend last commit
   git commit --amend -m "corrected message"
   ```

## **🎯 Project-Specific Commit Guidelines**

### **Monorepo Considerations**

When working in this monorepo, consider:

- **Use appropriate scopes** for different apps/packages
- **Test affected projects** before committing
- **Consider cross-package impacts**
- **Update documentation** when changing shared packages

### **Nx-Specific Commands**

```bash
# Test only affected projects
nx affected:test

# Build only affected projects
nx affected:build

# Lint only affected projects
nx affected:lint
```

### **Shared Package Changes**

When modifying shared packages:

```bash
# Always test consuming applications
pnpm run build
pnpm run test

# Use descriptive scopes
git commit -m "feat(ui): add new Button variants"
git commit -m "feat(shared): add User interface"
```

{"apps/api": {"root": ["apps/api/project.json", "nx/core/project-json"], "name": ["apps/api/project.json", "nx/core/project-json"], "tags": ["apps/api/package.json", "nx/core/package-json"], "tags.npm:public": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.2": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.3": ["apps/api/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.4": ["apps/api/package.json", "nx/core/package-json"], "metadata.description": ["apps/api/package.json", "nx/core/package-json"], "metadata.js": ["apps/api/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/api/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["apps/api/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/api/package.json", "nx/core/package-json"], "targets": ["apps/api/package.json", "nx/core/package-json"], "targets.start": ["apps/api/package.json", "nx/core/package-json"], "targets.start.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.start.options": ["apps/api/package.json", "nx/core/package-json"], "targets.start.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.start.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "targets.lint": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.options": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.options": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.lint:fix.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.options": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.db:generate.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.executor": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.options": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.metadata": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.options.script": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.metadata.scriptContent": ["apps/api/package.json", "nx/core/package-json"], "targets.db:migrate.metadata.runCommand": ["apps/api/package.json", "nx/core/package-json"], "sourceRoot": ["apps/api/project.json", "nx/core/project-json"], "targets.dev": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.executor": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.options": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.dependsOn": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.options.command": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.options.cwd": ["apps/api/project.json", "nx/core/project-json"], "targets.build": ["apps/api/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/api/project.json", "nx/core/project-json"], "targets.build.options": ["apps/api/project.json", "nx/core/project-json"], "targets.build.dependsOn": ["apps/api/project.json", "nx/core/project-json"], "targets.build.options.command": ["apps/api/project.json", "nx/core/project-json"], "targets.build.options.cwd": ["apps/api/project.json", "nx/core/project-json"], "targets.dev.cache": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/dashboard": {"root": ["apps/dashboard/project.json", "nx/core/project-json"], "name": ["apps/dashboard/project.json", "nx/core/project-json"], "tags": ["apps/dashboard/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.js": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/dashboard/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/dashboard/package.json", "nx/core/package-json"], "targets": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.executor": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.options": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.metadata": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.options.script": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.metadata.scriptContent": ["apps/dashboard/package.json", "nx/core/package-json"], "targets.lint:fix.metadata.runCommand": ["apps/dashboard/package.json", "nx/core/package-json"], "sourceRoot": ["apps/dashboard/project.json", "nx/core/project-json"], "projectType": ["apps/dashboard/project.json", "nx/core/project-json"], "tags.scope:dashboard": ["apps/dashboard/project.json", "nx/core/project-json"], "tags.type:app": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.dev": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.dev.executor": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.dev.options": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.dev.dependsOn": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.dev.options.command": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.dev.options.cwd": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.build": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.build.outputs": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.build.options": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.build.dependsOn": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.build.options.command": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.build.options.cwd": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.preview": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.preview.executor": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.preview.options": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.preview.dependsOn": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.preview.options.command": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.preview.options.cwd": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.lint": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.lint.executor": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.lint.options": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.lint.options.command": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.lint.options.cwd": ["apps/dashboard/project.json", "nx/core/project-json"], "targets.dev.cache": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "apps/marketing": {"root": ["apps/marketing/project.json", "nx/core/project-json"], "name": ["apps/marketing/project.json", "nx/core/project-json"], "tags": ["apps/marketing/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.1": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.js": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/marketing/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/marketing/package.json", "nx/core/package-json"], "targets": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.executor": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.options": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.metadata": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.options.script": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.metadata.scriptContent": ["apps/marketing/package.json", "nx/core/package-json"], "targets.start.metadata.runCommand": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.executor": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.options": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.metadata": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.options.script": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.metadata.scriptContent": ["apps/marketing/package.json", "nx/core/package-json"], "targets.lint.metadata.runCommand": ["apps/marketing/package.json", "nx/core/package-json"], "sourceRoot": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.executor": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.options": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.dependsOn": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.options.command": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.options.cwd": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.executor": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.options": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.dependsOn": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.options.command": ["apps/marketing/project.json", "nx/core/project-json"], "targets.build.options.cwd": ["apps/marketing/project.json", "nx/core/project-json"], "targets.dev.cache": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.cache": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/shared": {"root": ["packages/shared/project.json", "nx/core/project-json"], "name": ["packages/shared/project.json", "nx/core/project-json"], "tags": ["packages/shared/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/shared/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/shared/package.json", "nx/core/package-json"], "metadata.description": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/shared/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/shared/package.json", "nx/core/package-json"], "targets": ["packages/shared/package.json", "nx/core/package-json"], "targets.clean": ["packages/shared/package.json", "nx/core/package-json"], "targets.clean.executor": ["packages/shared/package.json", "nx/core/package-json"], "targets.clean.options": ["packages/shared/package.json", "nx/core/package-json"], "targets.clean.metadata": ["packages/shared/package.json", "nx/core/package-json"], "targets.clean.options.script": ["packages/shared/package.json", "nx/core/package-json"], "targets.clean.metadata.scriptContent": ["packages/shared/package.json", "nx/core/package-json"], "targets.clean.metadata.runCommand": ["packages/shared/package.json", "nx/core/package-json"], "sourceRoot": ["packages/shared/project.json", "nx/core/project-json"], "projectType": ["packages/shared/project.json", "nx/core/project-json"], "tags.scope:shared": ["packages/shared/project.json", "nx/core/project-json"], "tags.type:util": ["packages/shared/project.json", "nx/core/project-json"], "targets.dev": ["packages/shared/project.json", "nx/core/project-json"], "targets.dev.executor": ["packages/shared/project.json", "nx/core/project-json"], "targets.dev.options": ["packages/shared/project.json", "nx/core/project-json"], "targets.dev.options.command": ["packages/shared/project.json", "nx/core/project-json"], "targets.dev.options.cwd": ["packages/shared/project.json", "nx/core/project-json"], "targets.build": ["packages/shared/project.json", "nx/core/project-json"], "targets.build.executor": ["packages/shared/project.json", "nx/core/project-json"], "targets.build.outputs": ["packages/shared/project.json", "nx/core/project-json"], "targets.build.options": ["packages/shared/project.json", "nx/core/project-json"], "targets.build.cache": ["packages/shared/project.json", "nx/core/project-json"], "targets.build.options.command": ["packages/shared/project.json", "nx/core/project-json"], "targets.build.options.cwd": ["packages/shared/project.json", "nx/core/project-json"], "targets.dev.cache": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}, "packages/ui": {"root": ["packages/ui/project.json", "nx/core/project-json"], "name": ["packages/ui/project.json", "nx/core/project-json"], "tags": ["packages/ui/package.json", "nx/core/package-json"], "tags.npm:public": ["packages/ui/package.json", "nx/core/package-json"], "metadata.targetGroups": ["packages/ui/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts": ["packages/ui/package.json", "nx/core/package-json"], "metadata.targetGroups.NPM Scripts.0": ["packages/ui/package.json", "nx/core/package-json"], "metadata.description": ["packages/ui/package.json", "nx/core/package-json"], "metadata.js": ["packages/ui/package.json", "nx/core/package-json"], "metadata.js.packageName": ["packages/ui/package.json", "nx/core/package-json"], "metadata.js.packageMain": ["packages/ui/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["packages/ui/package.json", "nx/core/package-json"], "targets": ["packages/ui/package.json", "nx/core/package-json"], "targets.clean": ["packages/ui/package.json", "nx/core/package-json"], "targets.clean.executor": ["packages/ui/package.json", "nx/core/package-json"], "targets.clean.options": ["packages/ui/package.json", "nx/core/package-json"], "targets.clean.metadata": ["packages/ui/package.json", "nx/core/package-json"], "targets.clean.options.script": ["packages/ui/package.json", "nx/core/package-json"], "targets.clean.metadata.scriptContent": ["packages/ui/package.json", "nx/core/package-json"], "targets.clean.metadata.runCommand": ["packages/ui/package.json", "nx/core/package-json"], "sourceRoot": ["packages/ui/project.json", "nx/core/project-json"], "projectType": ["packages/ui/project.json", "nx/core/project-json"], "tags.scope:shared": ["packages/ui/project.json", "nx/core/project-json"], "tags.type:ui": ["packages/ui/project.json", "nx/core/project-json"], "targets.dev": ["packages/ui/project.json", "nx/core/project-json"], "targets.dev.executor": ["packages/ui/project.json", "nx/core/project-json"], "targets.dev.options": ["packages/ui/project.json", "nx/core/project-json"], "targets.dev.options.command": ["packages/ui/project.json", "nx/core/project-json"], "targets.dev.options.cwd": ["packages/ui/project.json", "nx/core/project-json"], "targets.build": ["packages/ui/project.json", "nx/core/project-json"], "targets.build.executor": ["packages/ui/project.json", "nx/core/project-json"], "targets.build.outputs": ["packages/ui/project.json", "nx/core/project-json"], "targets.build.options": ["packages/ui/project.json", "nx/core/project-json"], "targets.build.cache": ["packages/ui/project.json", "nx/core/project-json"], "targets.build.options.command": ["packages/ui/project.json", "nx/core/project-json"], "targets.build.options.cwd": ["packages/ui/project.json", "nx/core/project-json"], "targets.dev.cache": ["nx.json", "nx/target-defaults"], "targets.dev.parallelism": ["nx.json", "nx/target-defaults"], "targets.build.dependsOn": ["nx.json", "nx/target-defaults"], "targets.build.parallelism": ["nx.json", "nx/target-defaults"]}}
{"compilerOptions": {"target": "ES2020", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "outDir": "./dist", "rootDir": "./src", "declaration": true, "declarationMap": true, "jsx": "react-jsx", "noEmit": false, "composite": true, "module": "ESNext"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}
window.exclude = [];
  window.watch = true;
  window.environment = 'release';
  window.localMode = 'build';

  window.appConfig = {
    showDebugger: false,
    showExperimentalFeatures: false,
    workspaces: [
      {
        id: 'local',
        label: 'local',
        projectGraphUrl: 'project-graph.json',
        taskGraphUrl: 'task-graph.json',
        taskInputsUrl: 'task-inputs.json',
        sourceMapsUrl: 'source-maps.json'
      }
    ],
    defaultWorkspaceId: 'local',
  };
  window.projectGraphResponse = {"hash":"f70872efffbdc05ec29da44be1fe53eaf5837d567323440aab3693e9bd8e7e9f","projects":[{"name":"@mobility-network/shared","type":"lib","data":{"root":"packages/shared","name":"@mobility-network/shared","tags":["npm:public","scope:shared","type:util"],"metadata":{"targetGroups":{"NPM Scripts":["dev","clean"]},"description":"Shared types and utilities for the mobility network","js":{"packageName":"@mobility-network/shared","packageMain":"dist/index.js","isInPackageManagerWorkspaces":true}},"targets":{"dev":{"executor":"nx:run-script","options":{"script":"dev"},"metadata":{"scriptContent":"tsc --build --watch","runCommand":"pnpm run dev"},"configurations":{},"parallelism":true,"cache":false},"clean":{"executor":"nx:run-script","options":{"script":"clean"},"metadata":{"scriptContent":"rm -rf dist","runCommand":"pnpm run clean"},"configurations":{},"parallelism":true},"build":{"executor":"nx:run-commands","outputs":["{projectRoot}/dist"],"options":{"command":"tsc --build","cwd":"packages/shared"},"cache":true,"configurations":{},"parallelism":true,"dependsOn":["^build"]},"lint":{"executor":"nx:run-commands","options":{"command":"eslint src --ext ts","cwd":"packages/shared"},"configurations":{},"parallelism":true},"type-check":{"executor":"nx:run-commands","options":{"command":"tsc --noEmit","cwd":"packages/shared"},"configurations":{},"parallelism":true}},"sourceRoot":"packages/shared/src","projectType":"library","implicitDependencies":[]}},{"name":"@mobility-network/dashboard","type":"app","data":{"root":"apps/dashboard","name":"@mobility-network/dashboard","tags":["npm:private","scope:dashboard","type:app"],"metadata":{"targetGroups":{"NPM Scripts":["lint:fix"]},"js":{"packageName":"@mobility-network/dashboard","isInPackageManagerWorkspaces":true}},"targets":{"lint:fix":{"executor":"nx:run-script","options":{"script":"lint:fix"},"metadata":{"scriptContent":"eslint src --ext ts,tsx --fix","runCommand":"pnpm run lint:fix"},"configurations":{},"parallelism":true},"dev":{"executor":"nx:run-commands","options":{"command":"pnpm dev","cwd":"apps/dashboard"},"dependsOn":["@mobility-network/ui:build","@mobility-network/shared:build"],"configurations":{},"parallelism":true,"cache":false},"build":{"executor":"nx:run-commands","outputs":["{projectRoot}/dist"],"options":{"command":"pnpm build","cwd":"apps/dashboard"},"dependsOn":["@mobility-network/ui:build","@mobility-network/shared:build"],"configurations":{},"parallelism":true,"cache":true},"preview":{"executor":"nx:run-commands","options":{"command":"pnpm preview","cwd":"apps/dashboard"},"dependsOn":["build"],"configurations":{},"parallelism":true},"lint":{"executor":"nx:run-commands","options":{"command":"pnpm lint","cwd":"apps/dashboard"},"configurations":{},"parallelism":true}},"sourceRoot":"apps/dashboard/src","projectType":"application","implicitDependencies":[]}},{"name":"@mobility-network/marketing","type":"lib","data":{"root":"apps/marketing","name":"@mobility-network/marketing","tags":["npm:private"],"metadata":{"targetGroups":{"NPM Scripts":["start","lint"]},"js":{"packageName":"@mobility-network/marketing","isInPackageManagerWorkspaces":true}},"targets":{"start":{"executor":"nx:run-script","options":{"script":"start"},"metadata":{"scriptContent":"next start","runCommand":"pnpm run start"},"configurations":{},"parallelism":true},"lint":{"executor":"nx:run-script","options":{"script":"lint"},"metadata":{"scriptContent":"next lint","runCommand":"pnpm run lint"},"configurations":{},"parallelism":true},"dev":{"executor":"nx:run-commands","options":{"command":"pnpm dev","cwd":"apps/marketing"},"dependsOn":["@mobility-network/ui:build","@mobility-network/shared:build"],"configurations":{},"parallelism":true,"cache":false},"build":{"executor":"nx:run-commands","options":{"command":"pnpm build","cwd":"apps/marketing"},"dependsOn":["@mobility-network/ui:build","@mobility-network/shared:build"],"configurations":{},"parallelism":true,"cache":true}},"sourceRoot":"apps/marketing/src","implicitDependencies":[]}},{"name":"@mobility-network/ui","type":"lib","data":{"root":"packages/ui","name":"@mobility-network/ui","tags":["npm:public","scope:shared","type:ui"],"metadata":{"targetGroups":{"NPM Scripts":["dev","clean"]},"description":"","js":{"packageName":"@mobility-network/ui","packageMain":"dist/index.js","isInPackageManagerWorkspaces":true}},"targets":{"dev":{"executor":"nx:run-script","options":{"script":"dev"},"metadata":{"scriptContent":"tsc --build --watch","runCommand":"pnpm run dev"},"configurations":{},"parallelism":true,"cache":false},"clean":{"executor":"nx:run-script","options":{"script":"clean"},"metadata":{"scriptContent":"rm -rf dist","runCommand":"pnpm run clean"},"configurations":{},"parallelism":true},"build":{"executor":"nx:run-commands","outputs":["{projectRoot}/dist"],"options":{"command":"tsc --build","cwd":"packages/ui"},"cache":true,"configurations":{},"parallelism":true,"dependsOn":["^build"]},"lint":{"executor":"nx:run-commands","options":{"command":"eslint src --ext ts,tsx","cwd":"packages/ui"},"configurations":{},"parallelism":true},"type-check":{"executor":"nx:run-commands","options":{"command":"tsc --noEmit","cwd":"packages/ui"},"configurations":{},"parallelism":true}},"sourceRoot":"packages/ui/src","projectType":"library","implicitDependencies":[]}},{"name":"@mobility-network/api","type":"lib","data":{"root":"apps/api","name":"@mobility-network/api","tags":["npm:public"],"metadata":{"targetGroups":{"NPM Scripts":["start","lint","lint:fix","db:generate","db:migrate"]},"description":"","js":{"packageName":"@mobility-network/api","packageMain":"index.js","isInPackageManagerWorkspaces":true}},"targets":{"start":{"executor":"nx:run-script","options":{"script":"start"},"metadata":{"scriptContent":"node dist/index.js","runCommand":"pnpm run start"},"configurations":{},"parallelism":true},"lint":{"executor":"nx:run-script","options":{"script":"lint"},"metadata":{"scriptContent":"eslint . --ext ts","runCommand":"pnpm run lint"},"configurations":{},"parallelism":true},"lint:fix":{"executor":"nx:run-script","options":{"script":"lint:fix"},"metadata":{"scriptContent":"eslint . --ext ts --fix","runCommand":"pnpm run lint:fix"},"configurations":{},"parallelism":true},"db:generate":{"executor":"nx:run-script","options":{"script":"db:generate"},"metadata":{"scriptContent":"drizzle-kit generate:pg","runCommand":"pnpm run db:generate"},"configurations":{},"parallelism":true},"db:migrate":{"executor":"nx:run-script","options":{"script":"db:migrate"},"metadata":{"scriptContent":"drizzle-kit push:pg","runCommand":"pnpm run db:migrate"},"configurations":{},"parallelism":true},"dev":{"executor":"nx:run-commands","options":{"command":"pnpm dev","cwd":"apps/api"},"dependsOn":["@mobility-network/shared:build"],"configurations":{},"parallelism":true,"cache":false},"build":{"executor":"nx:run-commands","options":{"command":"pnpm build","cwd":"apps/api"},"dependsOn":["@mobility-network/shared:build"],"configurations":{},"parallelism":true,"cache":true}},"sourceRoot":"apps/api","implicitDependencies":[]}}],"dependencies":{"@mobility-network/shared":[],"@mobility-network/dashboard":[{"source":"@mobility-network/dashboard","target":"@mobility-network/ui","type":"static"},{"source":"@mobility-network/dashboard","target":"@mobility-network/shared","type":"static"}],"@mobility-network/marketing":[{"source":"@mobility-network/marketing","target":"@mobility-network/ui","type":"static"},{"source":"@mobility-network/marketing","target":"@mobility-network/shared","type":"static"}],"@mobility-network/ui":[],"@mobility-network/api":[{"source":"@mobility-network/api","target":"@mobility-network/shared","type":"static"}]},"fileMap":{"@mobility-network/marketing":[{"file":"apps/marketing/.gitignore","hash":"1867017717006394117"},{"file":"apps/marketing/README.md","hash":"10766794746169900117"},{"file":"apps/marketing/eslint.config.mjs","hash":"14633139915648186571","deps":["npm:@eslint/eslintrc"]},{"file":"apps/marketing/next.config.ts","hash":"2615010867795100795","deps":["npm:next"]},{"file":"apps/marketing/package.json","hash":"9104174263533673843","deps":["npm:typescript","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@tailwindcss/postcss","npm:tailwindcss","npm:eslint","npm:eslint-config-next","npm:@eslint/eslintrc","@mobility-network/ui","@mobility-network/shared","npm:react","npm:react-dom","npm:next"]},{"file":"apps/marketing/postcss.config.mjs","hash":"4766641093237987671"},{"file":"apps/marketing/project.json","hash":"2012609391965606650"},{"file":"apps/marketing/public/file.svg","hash":"15617830351863523835"},{"file":"apps/marketing/public/globe.svg","hash":"9134466864871335697"},{"file":"apps/marketing/public/next.svg","hash":"8300767200008744458"},{"file":"apps/marketing/public/vercel.svg","hash":"3007623926711696394"},{"file":"apps/marketing/public/window.svg","hash":"16677869292061012332"},{"file":"apps/marketing/src/app/favicon.ico","hash":"9157999678830540297"},{"file":"apps/marketing/src/app/globals.css","hash":"7980784277252421672"},{"file":"apps/marketing/src/app/layout.tsx","hash":"4622598555729013549","deps":["npm:next"]},{"file":"apps/marketing/src/app/page.tsx","hash":"11857874891651862620","deps":["npm:next"]},{"file":"apps/marketing/tsconfig.json","hash":"2587473170689616595"}],"@mobility-network/dashboard":[{"file":"apps/dashboard/.gitignore","hash":"13046175384202504187"},{"file":"apps/dashboard/README.md","hash":"13160460843517416105"},{"file":"apps/dashboard/eslint.config.js","hash":"13792119130412869889","deps":["npm:@eslint/js","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:globals","npm:typescript-eslint"]},{"file":"apps/dashboard/index.html","hash":"4025202329384162906"},{"file":"apps/dashboard/package.json","hash":"8716937752385357334","deps":["npm:@eslint/js","npm:@types/react","npm:@types/react-dom","npm:@vitejs/plugin-react","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:globals","npm:typescript","npm:typescript-eslint","npm:vite","@mobility-network/ui","@mobility-network/shared","npm:@tanstack/react-query","npm:axios","npm:react","npm:react-dom"]},{"file":"apps/dashboard/project.json","hash":"15810958370192374265"},{"file":"apps/dashboard/public/vite.svg","hash":"16046085400642193116"},{"file":"apps/dashboard/src/App.css","hash":"3739391659270974612"},{"file":"apps/dashboard/src/App.tsx","hash":"17982990676225942543","deps":["npm:react"]},{"file":"apps/dashboard/src/assets/react.svg","hash":"16069471864800376455"},{"file":"apps/dashboard/src/index.css","hash":"13504153635654440336"},{"file":"apps/dashboard/src/main.tsx","hash":"9699625239573703184","deps":["npm:react","npm:react-dom"]},{"file":"apps/dashboard/src/vite-env.d.ts","hash":"12946363841406869089"},{"file":"apps/dashboard/tsconfig.app.json","hash":"13773565989543243116"},{"file":"apps/dashboard/tsconfig.json","hash":"2365188958523842680"},{"file":"apps/dashboard/tsconfig.node.json","hash":"3588689360788834997"},{"file":"apps/dashboard/vite.config.ts","hash":"14628939841492078449","deps":["npm:@vitejs/plugin-react","npm:vite"]}],"@mobility-network/ui":[{"file":"packages/ui/package.json","hash":"7927830995134817660","deps":["npm:@types/react","npm:@types/react-dom","npm:typescript","npm:react","npm:react-dom"]},{"file":"packages/ui/project.json","hash":"12406679067865159624"},{"file":"packages/ui/src/Button.tsx","hash":"4316814734833712178","deps":["npm:react"]},{"file":"packages/ui/src/index.ts","hash":"1548372080853166209"},{"file":"packages/ui/tsconfig.json","hash":"13599227607190410508"}],"@mobility-network/shared":[{"file":"packages/shared/package.json","hash":"2184464834947991670","deps":["npm:typescript"]},{"file":"packages/shared/project.json","hash":"8652584600043160944"},{"file":"packages/shared/src/index.ts","hash":"18260907589837219459"},{"file":"packages/shared/src/type.ts","hash":"1541546793952677385"},{"file":"packages/shared/tsconfig.json","hash":"13599227607190410508"}],"@mobility-network/api":[{"file":"apps/api/index.ts","hash":"13138597226221765836","deps":["npm:cors","npm:express","npm:helmet","npm:morgan"]},{"file":"apps/api/package.json","hash":"12642920699240696457","deps":["npm:@types/cors","npm:@types/express","npm:@types/helmet","npm:@types/morgan","npm:@types/node@24.0.3","npm:nodemon","npm:ts-node","npm:typescript","@mobility-network/shared","npm:cors","npm:express","npm:helmet","npm:morgan"]},{"file":"apps/api/project.json","hash":"12683448438150714954"},{"file":"apps/api/tsconfig.json","hash":"5465089415256029786"}]},"layout":{"appsDir":"apps","libsDir":"libs"},"affected":[],"focus":null,"groupByFolder":false,"exclude":[],"isPartial":false,"connectedToCloud":true};
    window.taskGraphResponse = {"taskGraphs":{"@mobility-network/shared:dev":{"roots":["@mobility-network/shared:dev"],"tasks":{"@mobility-network/shared:dev":{"id":"@mobility-network/shared:dev","target":{"project":"@mobility-network/shared","target":"dev"},"projectRoot":"packages/shared","overrides":{},"outputs":[],"cache":false,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/shared:dev":[]},"continuousDependencies":{"@mobility-network/shared:dev":[]}},"@mobility-network/shared:clean":{"roots":["@mobility-network/shared:clean"],"tasks":{"@mobility-network/shared:clean":{"id":"@mobility-network/shared:clean","target":{"project":"@mobility-network/shared","target":"clean"},"projectRoot":"packages/shared","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/shared:clean":[]},"continuousDependencies":{"@mobility-network/shared:clean":[]}},"@mobility-network/shared:build":{"roots":["@mobility-network/shared:build"],"tasks":{"@mobility-network/shared:build":{"id":"@mobility-network/shared:build","target":{"project":"@mobility-network/shared","target":"build"},"projectRoot":"packages/shared","overrides":{},"outputs":["packages/shared/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/shared:build":[]},"continuousDependencies":{"@mobility-network/shared:build":[]}},"@mobility-network/shared:lint":{"roots":["@mobility-network/shared:lint"],"tasks":{"@mobility-network/shared:lint":{"id":"@mobility-network/shared:lint","target":{"project":"@mobility-network/shared","target":"lint"},"projectRoot":"packages/shared","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/shared:lint":[]},"continuousDependencies":{"@mobility-network/shared:lint":[]}},"@mobility-network/shared:type-check":{"roots":["@mobility-network/shared:type-check"],"tasks":{"@mobility-network/shared:type-check":{"id":"@mobility-network/shared:type-check","target":{"project":"@mobility-network/shared","target":"type-check"},"projectRoot":"packages/shared","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/shared:type-check":[]},"continuousDependencies":{"@mobility-network/shared:type-check":[]}},"@mobility-network/dashboard:lint:fix":{"roots":["@mobility-network/dashboard:lint:fix"],"tasks":{"@mobility-network/dashboard:lint:fix":{"id":"@mobility-network/dashboard:lint:fix","target":{"project":"@mobility-network/dashboard","target":"lint:fix"},"projectRoot":"apps/dashboard","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/dashboard:lint:fix":[]},"continuousDependencies":{"@mobility-network/dashboard:lint:fix":[]}},"@mobility-network/dashboard:dev":{"roots":["@mobility-network/ui:build","@mobility-network/shared:build"],"tasks":{"@mobility-network/dashboard:dev":{"id":"@mobility-network/dashboard:dev","target":{"project":"@mobility-network/dashboard","target":"dev"},"projectRoot":"apps/dashboard","overrides":{},"outputs":[],"cache":false,"parallelism":true,"continuous":false},"@mobility-network/ui:build":{"id":"@mobility-network/ui:build","target":{"project":"@mobility-network/ui","target":"build"},"projectRoot":"packages/ui","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/ui/dist"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/shared:build":{"id":"@mobility-network/shared:build","target":{"project":"@mobility-network/shared","target":"build"},"projectRoot":"packages/shared","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/shared/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/dashboard:dev":["@mobility-network/ui:build","@mobility-network/shared:build"],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]},"continuousDependencies":{"@mobility-network/dashboard:dev":[],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]}},"@mobility-network/dashboard:build":{"roots":["@mobility-network/ui:build","@mobility-network/shared:build"],"tasks":{"@mobility-network/dashboard:build":{"id":"@mobility-network/dashboard:build","target":{"project":"@mobility-network/dashboard","target":"build"},"projectRoot":"apps/dashboard","overrides":{},"outputs":["apps/dashboard/dist"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/ui:build":{"id":"@mobility-network/ui:build","target":{"project":"@mobility-network/ui","target":"build"},"projectRoot":"packages/ui","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/ui/dist"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/shared:build":{"id":"@mobility-network/shared:build","target":{"project":"@mobility-network/shared","target":"build"},"projectRoot":"packages/shared","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/shared/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/dashboard:build":["@mobility-network/ui:build","@mobility-network/shared:build"],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]},"continuousDependencies":{"@mobility-network/dashboard:build":[],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]}},"@mobility-network/dashboard:preview":{"roots":["@mobility-network/ui:build","@mobility-network/shared:build"],"tasks":{"@mobility-network/dashboard:preview":{"id":"@mobility-network/dashboard:preview","target":{"project":"@mobility-network/dashboard","target":"preview"},"projectRoot":"apps/dashboard","overrides":{},"outputs":[],"parallelism":true,"continuous":false},"@mobility-network/dashboard:build":{"id":"@mobility-network/dashboard:build","target":{"project":"@mobility-network/dashboard","target":"build"},"projectRoot":"apps/dashboard","overrides":{"__overrides_unparsed__":[]},"outputs":["apps/dashboard/dist"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/ui:build":{"id":"@mobility-network/ui:build","target":{"project":"@mobility-network/ui","target":"build"},"projectRoot":"packages/ui","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/ui/dist"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/shared:build":{"id":"@mobility-network/shared:build","target":{"project":"@mobility-network/shared","target":"build"},"projectRoot":"packages/shared","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/shared/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/dashboard:preview":["@mobility-network/dashboard:build"],"@mobility-network/dashboard:build":["@mobility-network/ui:build","@mobility-network/shared:build"],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]},"continuousDependencies":{"@mobility-network/dashboard:preview":[],"@mobility-network/dashboard:build":[],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]}},"@mobility-network/dashboard:lint":{"roots":["@mobility-network/dashboard:lint"],"tasks":{"@mobility-network/dashboard:lint":{"id":"@mobility-network/dashboard:lint","target":{"project":"@mobility-network/dashboard","target":"lint"},"projectRoot":"apps/dashboard","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/dashboard:lint":[]},"continuousDependencies":{"@mobility-network/dashboard:lint":[]}},"@mobility-network/marketing:start":{"roots":["@mobility-network/marketing:start"],"tasks":{"@mobility-network/marketing:start":{"id":"@mobility-network/marketing:start","target":{"project":"@mobility-network/marketing","target":"start"},"projectRoot":"apps/marketing","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/marketing:start":[]},"continuousDependencies":{"@mobility-network/marketing:start":[]}},"@mobility-network/marketing:lint":{"roots":["@mobility-network/marketing:lint"],"tasks":{"@mobility-network/marketing:lint":{"id":"@mobility-network/marketing:lint","target":{"project":"@mobility-network/marketing","target":"lint"},"projectRoot":"apps/marketing","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/marketing:lint":[]},"continuousDependencies":{"@mobility-network/marketing:lint":[]}},"@mobility-network/marketing:dev":{"roots":["@mobility-network/ui:build","@mobility-network/shared:build"],"tasks":{"@mobility-network/marketing:dev":{"id":"@mobility-network/marketing:dev","target":{"project":"@mobility-network/marketing","target":"dev"},"projectRoot":"apps/marketing","overrides":{},"outputs":[],"cache":false,"parallelism":true,"continuous":false},"@mobility-network/ui:build":{"id":"@mobility-network/ui:build","target":{"project":"@mobility-network/ui","target":"build"},"projectRoot":"packages/ui","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/ui/dist"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/shared:build":{"id":"@mobility-network/shared:build","target":{"project":"@mobility-network/shared","target":"build"},"projectRoot":"packages/shared","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/shared/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/marketing:dev":["@mobility-network/ui:build","@mobility-network/shared:build"],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]},"continuousDependencies":{"@mobility-network/marketing:dev":[],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]}},"@mobility-network/marketing:build":{"roots":["@mobility-network/ui:build","@mobility-network/shared:build"],"tasks":{"@mobility-network/marketing:build":{"id":"@mobility-network/marketing:build","target":{"project":"@mobility-network/marketing","target":"build"},"projectRoot":"apps/marketing","overrides":{},"outputs":["dist/apps/marketing","apps/marketing/dist","apps/marketing/build","apps/marketing/public"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/ui:build":{"id":"@mobility-network/ui:build","target":{"project":"@mobility-network/ui","target":"build"},"projectRoot":"packages/ui","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/ui/dist"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/shared:build":{"id":"@mobility-network/shared:build","target":{"project":"@mobility-network/shared","target":"build"},"projectRoot":"packages/shared","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/shared/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/marketing:build":["@mobility-network/ui:build","@mobility-network/shared:build"],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]},"continuousDependencies":{"@mobility-network/marketing:build":[],"@mobility-network/ui:build":[],"@mobility-network/shared:build":[]}},"@mobility-network/ui:dev":{"roots":["@mobility-network/ui:dev"],"tasks":{"@mobility-network/ui:dev":{"id":"@mobility-network/ui:dev","target":{"project":"@mobility-network/ui","target":"dev"},"projectRoot":"packages/ui","overrides":{},"outputs":[],"cache":false,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/ui:dev":[]},"continuousDependencies":{"@mobility-network/ui:dev":[]}},"@mobility-network/ui:clean":{"roots":["@mobility-network/ui:clean"],"tasks":{"@mobility-network/ui:clean":{"id":"@mobility-network/ui:clean","target":{"project":"@mobility-network/ui","target":"clean"},"projectRoot":"packages/ui","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/ui:clean":[]},"continuousDependencies":{"@mobility-network/ui:clean":[]}},"@mobility-network/ui:build":{"roots":["@mobility-network/ui:build"],"tasks":{"@mobility-network/ui:build":{"id":"@mobility-network/ui:build","target":{"project":"@mobility-network/ui","target":"build"},"projectRoot":"packages/ui","overrides":{},"outputs":["packages/ui/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/ui:build":[]},"continuousDependencies":{"@mobility-network/ui:build":[]}},"@mobility-network/ui:lint":{"roots":["@mobility-network/ui:lint"],"tasks":{"@mobility-network/ui:lint":{"id":"@mobility-network/ui:lint","target":{"project":"@mobility-network/ui","target":"lint"},"projectRoot":"packages/ui","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/ui:lint":[]},"continuousDependencies":{"@mobility-network/ui:lint":[]}},"@mobility-network/ui:type-check":{"roots":["@mobility-network/ui:type-check"],"tasks":{"@mobility-network/ui:type-check":{"id":"@mobility-network/ui:type-check","target":{"project":"@mobility-network/ui","target":"type-check"},"projectRoot":"packages/ui","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/ui:type-check":[]},"continuousDependencies":{"@mobility-network/ui:type-check":[]}},"@mobility-network/api:start":{"roots":["@mobility-network/api:start"],"tasks":{"@mobility-network/api:start":{"id":"@mobility-network/api:start","target":{"project":"@mobility-network/api","target":"start"},"projectRoot":"apps/api","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/api:start":[]},"continuousDependencies":{"@mobility-network/api:start":[]}},"@mobility-network/api:lint":{"roots":["@mobility-network/api:lint"],"tasks":{"@mobility-network/api:lint":{"id":"@mobility-network/api:lint","target":{"project":"@mobility-network/api","target":"lint"},"projectRoot":"apps/api","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/api:lint":[]},"continuousDependencies":{"@mobility-network/api:lint":[]}},"@mobility-network/api:lint:fix":{"roots":["@mobility-network/api:lint:fix"],"tasks":{"@mobility-network/api:lint:fix":{"id":"@mobility-network/api:lint:fix","target":{"project":"@mobility-network/api","target":"lint:fix"},"projectRoot":"apps/api","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/api:lint:fix":[]},"continuousDependencies":{"@mobility-network/api:lint:fix":[]}},"@mobility-network/api:db:generate":{"roots":["@mobility-network/api:db:generate"],"tasks":{"@mobility-network/api:db:generate":{"id":"@mobility-network/api:db:generate","target":{"project":"@mobility-network/api","target":"db:generate"},"projectRoot":"apps/api","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/api:db:generate":[]},"continuousDependencies":{"@mobility-network/api:db:generate":[]}},"@mobility-network/api:db:migrate":{"roots":["@mobility-network/api:db:migrate"],"tasks":{"@mobility-network/api:db:migrate":{"id":"@mobility-network/api:db:migrate","target":{"project":"@mobility-network/api","target":"db:migrate"},"projectRoot":"apps/api","overrides":{},"outputs":[],"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/api:db:migrate":[]},"continuousDependencies":{"@mobility-network/api:db:migrate":[]}},"@mobility-network/api:dev":{"roots":["@mobility-network/shared:build"],"tasks":{"@mobility-network/api:dev":{"id":"@mobility-network/api:dev","target":{"project":"@mobility-network/api","target":"dev"},"projectRoot":"apps/api","overrides":{},"outputs":[],"cache":false,"parallelism":true,"continuous":false},"@mobility-network/shared:build":{"id":"@mobility-network/shared:build","target":{"project":"@mobility-network/shared","target":"build"},"projectRoot":"packages/shared","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/shared/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/api:dev":["@mobility-network/shared:build"],"@mobility-network/shared:build":[]},"continuousDependencies":{"@mobility-network/api:dev":[],"@mobility-network/shared:build":[]}},"@mobility-network/api:build":{"roots":["@mobility-network/shared:build"],"tasks":{"@mobility-network/api:build":{"id":"@mobility-network/api:build","target":{"project":"@mobility-network/api","target":"build"},"projectRoot":"apps/api","overrides":{},"outputs":["dist/apps/api","apps/api/dist","apps/api/build","apps/api/public"],"cache":true,"parallelism":true,"continuous":false},"@mobility-network/shared:build":{"id":"@mobility-network/shared:build","target":{"project":"@mobility-network/shared","target":"build"},"projectRoot":"packages/shared","overrides":{"__overrides_unparsed__":[]},"outputs":["packages/shared/dist"],"cache":true,"parallelism":true,"continuous":false}},"dependencies":{"@mobility-network/api:build":["@mobility-network/shared:build"],"@mobility-network/shared:build":[]},"continuousDependencies":{"@mobility-network/api:build":[],"@mobility-network/shared:build":[]}}},"errors":{},"plans":{"@mobility-network/shared:dev":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/shared:ProjectConfiguration","@mobility-network/shared:TsConfig","npm:typescript","AllExternalDependencies"],"@mobility-network/shared:clean":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/shared:ProjectConfiguration","@mobility-network/shared:TsConfig","npm:typescript","AllExternalDependencies"],"@mobility-network/shared:build":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/shared:ProjectConfiguration","@mobility-network/shared:TsConfig","npm:typescript","AllExternalDependencies"],"@mobility-network/shared:lint":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/shared:ProjectConfiguration","@mobility-network/shared:TsConfig","npm:typescript","AllExternalDependencies"],"@mobility-network/shared:type-check":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/shared:ProjectConfiguration","@mobility-network/shared:TsConfig","npm:typescript","AllExternalDependencies"],"@mobility-network/dashboard:lint:fix":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/dashboard:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/dashboard:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/dashboard:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/dashboard:dev":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/dashboard:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/dashboard:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/dashboard:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/ui:build":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/ui:ProjectConfiguration","@mobility-network/ui:TsConfig","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript","AllExternalDependencies"],"@mobility-network/dashboard:build":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/dashboard:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/dashboard:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/dashboard:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/dashboard:preview":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/dashboard:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/dashboard:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/dashboard:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/dashboard:lint":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/dashboard:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/dashboard:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/dashboard:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/marketing:start":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/marketing:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/marketing:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/marketing:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@alloc/quick-lru","npm:@ampproject/remapping","npm:@emnapi/core","npm:@emnapi/runtime","npm:@emnapi/wasi-threads","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@img/sharp-darwin-arm64","npm:@img/sharp-darwin-x64","npm:@img/sharp-libvips-darwin-arm64","npm:@img/sharp-libvips-darwin-x64","npm:@img/sharp-libvips-linux-arm","npm:@img/sharp-libvips-linux-arm64","npm:@img/sharp-libvips-linux-ppc64","npm:@img/sharp-libvips-linux-s390x","npm:@img/sharp-libvips-linux-x64","npm:@img/sharp-libvips-linuxmusl-arm64","npm:@img/sharp-libvips-linuxmusl-x64","npm:@img/sharp-linux-arm","npm:@img/sharp-linux-arm64","npm:@img/sharp-linux-s390x","npm:@img/sharp-linux-x64","npm:@img/sharp-linuxmusl-arm64","npm:@img/sharp-linuxmusl-x64","npm:@img/sharp-wasm32","npm:@img/sharp-win32-arm64","npm:@img/sharp-win32-ia32","npm:@img/sharp-win32-x64","npm:@isaacs/fs-minipass","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@napi-rs/wasm-runtime@0.2.11","npm:@next/env","npm:@next/eslint-plugin-next","npm:@next/swc-darwin-arm64","npm:@next/swc-darwin-x64","npm:@next/swc-linux-arm64-gnu","npm:@next/swc-linux-arm64-musl","npm:@next/swc-linux-x64-gnu","npm:@next/swc-linux-x64-musl","npm:@next/swc-win32-arm64-msvc","npm:@next/swc-win32-x64-msvc","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@nolyfill/is-core-module","npm:@rtsao/scc","npm:@rushstack/eslint-patch","npm:@swc/counter","npm:@swc/helpers","npm:@tailwindcss/node","npm:@tailwindcss/oxide","npm:@tailwindcss/oxide-android-arm64","npm:@tailwindcss/oxide-darwin-arm64","npm:@tailwindcss/oxide-darwin-x64","npm:@tailwindcss/oxide-freebsd-x64","npm:@tailwindcss/oxide-linux-arm-gnueabihf","npm:@tailwindcss/oxide-linux-arm64-gnu","npm:@tailwindcss/oxide-linux-arm64-musl","npm:@tailwindcss/oxide-linux-x64-gnu","npm:@tailwindcss/oxide-linux-x64-musl","npm:@tailwindcss/oxide-wasm32-wasi","npm:@tailwindcss/oxide-win32-arm64-msvc","npm:@tailwindcss/oxide-win32-x64-msvc","npm:@tailwindcss/postcss","npm:@tybys/wasm-util","npm:@types/estree","npm:@types/json-schema","npm:@types/json5","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@unrs/resolver-binding-android-arm-eabi","npm:@unrs/resolver-binding-android-arm64","npm:@unrs/resolver-binding-darwin-arm64","npm:@unrs/resolver-binding-darwin-x64","npm:@unrs/resolver-binding-freebsd-x64","npm:@unrs/resolver-binding-linux-arm-gnueabihf","npm:@unrs/resolver-binding-linux-arm-musleabihf","npm:@unrs/resolver-binding-linux-arm64-gnu","npm:@unrs/resolver-binding-linux-arm64-musl","npm:@unrs/resolver-binding-linux-ppc64-gnu","npm:@unrs/resolver-binding-linux-riscv64-gnu","npm:@unrs/resolver-binding-linux-riscv64-musl","npm:@unrs/resolver-binding-linux-s390x-gnu","npm:@unrs/resolver-binding-linux-x64-gnu","npm:@unrs/resolver-binding-linux-x64-musl","npm:@unrs/resolver-binding-wasm32-wasi","npm:@unrs/resolver-binding-win32-arm64-msvc","npm:@unrs/resolver-binding-win32-ia32-msvc","npm:@unrs/resolver-binding-win32-x64-msvc","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:aria-query","npm:array-buffer-byte-length","npm:array-includes","npm:array.prototype.findlast","npm:array.prototype.findlastindex","npm:array.prototype.flat","npm:array.prototype.flatmap","npm:array.prototype.tosorted","npm:arraybuffer.prototype.slice","npm:ast-types-flow","npm:async-function","npm:available-typed-arrays","npm:axe-core","npm:axobject-query","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:busboy","npm:call-bind","npm:call-bind-apply-helpers","npm:call-bound","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:chownr","npm:client-only","npm:color","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:color-string","npm:concat-map","npm:cross-spawn","npm:csstype","npm:damerau-levenshtein","npm:data-view-buffer","npm:data-view-byte-length","npm:data-view-byte-offset","npm:debug@3.2.7","npm:debug@4.4.1","npm:deep-is","npm:define-data-property","npm:define-properties","npm:detect-libc","npm:doctrine","npm:dunder-proto","npm:emoji-regex@9.2.2","npm:enhanced-resolve","npm:es-abstract","npm:es-define-property","npm:es-errors","npm:es-iterator-helpers","npm:es-object-atoms","npm:es-set-tostringtag","npm:es-shim-unscopables","npm:es-to-primitive","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-config-next","npm:eslint-import-context","npm:eslint-import-resolver-node","npm:eslint-import-resolver-typescript","npm:eslint-import-resolver-typescript@3.10.1","npm:eslint-module-utils","npm:eslint-plugin-import","npm:eslint-plugin-jsx-a11y","npm:eslint-plugin-react","npm:eslint-plugin-react-hooks","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.1","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:for-each","npm:function-bind","npm:function.prototype.name","npm:functions-have-names","npm:get-intrinsic","npm:get-proto","npm:get-symbol-description","npm:get-tsconfig","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals@14.0.0","npm:globalthis","npm:gopd","npm:graceful-fs","npm:graphemer","npm:has-bigints","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-property-descriptors","npm:has-proto","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:internal-slot","npm:is-array-buffer","npm:is-arrayish@0.3.2","npm:is-async-function","npm:is-bigint","npm:is-boolean-object","npm:is-bun-module","npm:is-callable","npm:is-core-module","npm:is-data-view","npm:is-date-object","npm:is-extglob","npm:is-finalizationregistry","npm:is-generator-function","npm:is-glob","npm:is-map","npm:is-negative-zero","npm:is-number","npm:is-number-object","npm:is-regex","npm:is-set","npm:is-shared-array-buffer","npm:is-string","npm:is-symbol","npm:is-typed-array","npm:is-weakmap","npm:is-weakref","npm:is-weakset","npm:isarray","npm:isexe","npm:iterator.prototype","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@1.0.2","npm:jsx-ast-utils","npm:keyv","npm:language-subtag-registry","npm:language-tags","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:loose-envify","npm:magic-string","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:minimist@1.2.8","npm:minipass","npm:minizlib","npm:mkdirp","npm:ms@2.1.3","npm:nanoid","npm:napi-postinstall","npm:natural-compare","npm:next","npm:object-assign","npm:object-inspect","npm:object-keys","npm:object.assign","npm:object.entries","npm:object.fromentries","npm:object.groupby","npm:object.values","npm:optionator","npm:own-keys","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:path-parse","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:possible-typed-array-names","npm:postcss@8.4.31","npm:postcss@8.5.6","npm:prelude-ls","npm:prop-types","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-is@16.13.1","npm:reflect.getprototypeof","npm:regexp.prototype.flags","npm:resolve-from@4.0.0","npm:resolve-pkg-maps","npm:resolve@1.22.10","npm:resolve@2.0.0-next.5","npm:reusify","npm:run-parallel","npm:safe-array-concat","npm:safe-push-apply","npm:safe-regex-test","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:set-function-length","npm:set-function-name","npm:set-proto","npm:sharp","npm:shebang-command","npm:shebang-regex","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-swizzle","npm:source-map-js","npm:stable-hash","npm:stable-hash-x","npm:stop-iteration-iterator","npm:streamsearch","npm:string.prototype.includes","npm:string.prototype.matchall","npm:string.prototype.repeat","npm:string.prototype.trim","npm:string.prototype.trimend","npm:string.prototype.trimstart","npm:strip-bom@3.0.0","npm:strip-json-comments","npm:styled-jsx","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:supports-preserve-symlinks-flag","npm:tailwindcss","npm:tapable","npm:tar","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:tsconfig-paths@3.15.0","npm:tslib","npm:type-check","npm:typed-array-buffer","npm:typed-array-byte-length","npm:typed-array-byte-offset","npm:typed-array-length","npm:typescript","npm:unbox-primitive","npm:undici-types@6.21.0","npm:unrs-resolver","npm:uri-js","npm:which-boxed-primitive","npm:which-builtin-type","npm:which-collection","npm:which-typed-array","npm:which@2.0.2","npm:word-wrap","npm:yallist@5.0.0","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/marketing:lint":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/marketing:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/marketing:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/marketing:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@alloc/quick-lru","npm:@ampproject/remapping","npm:@emnapi/core","npm:@emnapi/runtime","npm:@emnapi/wasi-threads","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@img/sharp-darwin-arm64","npm:@img/sharp-darwin-x64","npm:@img/sharp-libvips-darwin-arm64","npm:@img/sharp-libvips-darwin-x64","npm:@img/sharp-libvips-linux-arm","npm:@img/sharp-libvips-linux-arm64","npm:@img/sharp-libvips-linux-ppc64","npm:@img/sharp-libvips-linux-s390x","npm:@img/sharp-libvips-linux-x64","npm:@img/sharp-libvips-linuxmusl-arm64","npm:@img/sharp-libvips-linuxmusl-x64","npm:@img/sharp-linux-arm","npm:@img/sharp-linux-arm64","npm:@img/sharp-linux-s390x","npm:@img/sharp-linux-x64","npm:@img/sharp-linuxmusl-arm64","npm:@img/sharp-linuxmusl-x64","npm:@img/sharp-wasm32","npm:@img/sharp-win32-arm64","npm:@img/sharp-win32-ia32","npm:@img/sharp-win32-x64","npm:@isaacs/fs-minipass","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@napi-rs/wasm-runtime@0.2.11","npm:@next/env","npm:@next/eslint-plugin-next","npm:@next/swc-darwin-arm64","npm:@next/swc-darwin-x64","npm:@next/swc-linux-arm64-gnu","npm:@next/swc-linux-arm64-musl","npm:@next/swc-linux-x64-gnu","npm:@next/swc-linux-x64-musl","npm:@next/swc-win32-arm64-msvc","npm:@next/swc-win32-x64-msvc","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@nolyfill/is-core-module","npm:@rtsao/scc","npm:@rushstack/eslint-patch","npm:@swc/counter","npm:@swc/helpers","npm:@tailwindcss/node","npm:@tailwindcss/oxide","npm:@tailwindcss/oxide-android-arm64","npm:@tailwindcss/oxide-darwin-arm64","npm:@tailwindcss/oxide-darwin-x64","npm:@tailwindcss/oxide-freebsd-x64","npm:@tailwindcss/oxide-linux-arm-gnueabihf","npm:@tailwindcss/oxide-linux-arm64-gnu","npm:@tailwindcss/oxide-linux-arm64-musl","npm:@tailwindcss/oxide-linux-x64-gnu","npm:@tailwindcss/oxide-linux-x64-musl","npm:@tailwindcss/oxide-wasm32-wasi","npm:@tailwindcss/oxide-win32-arm64-msvc","npm:@tailwindcss/oxide-win32-x64-msvc","npm:@tailwindcss/postcss","npm:@tybys/wasm-util","npm:@types/estree","npm:@types/json-schema","npm:@types/json5","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@unrs/resolver-binding-android-arm-eabi","npm:@unrs/resolver-binding-android-arm64","npm:@unrs/resolver-binding-darwin-arm64","npm:@unrs/resolver-binding-darwin-x64","npm:@unrs/resolver-binding-freebsd-x64","npm:@unrs/resolver-binding-linux-arm-gnueabihf","npm:@unrs/resolver-binding-linux-arm-musleabihf","npm:@unrs/resolver-binding-linux-arm64-gnu","npm:@unrs/resolver-binding-linux-arm64-musl","npm:@unrs/resolver-binding-linux-ppc64-gnu","npm:@unrs/resolver-binding-linux-riscv64-gnu","npm:@unrs/resolver-binding-linux-riscv64-musl","npm:@unrs/resolver-binding-linux-s390x-gnu","npm:@unrs/resolver-binding-linux-x64-gnu","npm:@unrs/resolver-binding-linux-x64-musl","npm:@unrs/resolver-binding-wasm32-wasi","npm:@unrs/resolver-binding-win32-arm64-msvc","npm:@unrs/resolver-binding-win32-ia32-msvc","npm:@unrs/resolver-binding-win32-x64-msvc","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:aria-query","npm:array-buffer-byte-length","npm:array-includes","npm:array.prototype.findlast","npm:array.prototype.findlastindex","npm:array.prototype.flat","npm:array.prototype.flatmap","npm:array.prototype.tosorted","npm:arraybuffer.prototype.slice","npm:ast-types-flow","npm:async-function","npm:available-typed-arrays","npm:axe-core","npm:axobject-query","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:busboy","npm:call-bind","npm:call-bind-apply-helpers","npm:call-bound","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:chownr","npm:client-only","npm:color","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:color-string","npm:concat-map","npm:cross-spawn","npm:csstype","npm:damerau-levenshtein","npm:data-view-buffer","npm:data-view-byte-length","npm:data-view-byte-offset","npm:debug@3.2.7","npm:debug@4.4.1","npm:deep-is","npm:define-data-property","npm:define-properties","npm:detect-libc","npm:doctrine","npm:dunder-proto","npm:emoji-regex@9.2.2","npm:enhanced-resolve","npm:es-abstract","npm:es-define-property","npm:es-errors","npm:es-iterator-helpers","npm:es-object-atoms","npm:es-set-tostringtag","npm:es-shim-unscopables","npm:es-to-primitive","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-config-next","npm:eslint-import-context","npm:eslint-import-resolver-node","npm:eslint-import-resolver-typescript","npm:eslint-import-resolver-typescript@3.10.1","npm:eslint-module-utils","npm:eslint-plugin-import","npm:eslint-plugin-jsx-a11y","npm:eslint-plugin-react","npm:eslint-plugin-react-hooks","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.1","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:for-each","npm:function-bind","npm:function.prototype.name","npm:functions-have-names","npm:get-intrinsic","npm:get-proto","npm:get-symbol-description","npm:get-tsconfig","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals@14.0.0","npm:globalthis","npm:gopd","npm:graceful-fs","npm:graphemer","npm:has-bigints","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-property-descriptors","npm:has-proto","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:internal-slot","npm:is-array-buffer","npm:is-arrayish@0.3.2","npm:is-async-function","npm:is-bigint","npm:is-boolean-object","npm:is-bun-module","npm:is-callable","npm:is-core-module","npm:is-data-view","npm:is-date-object","npm:is-extglob","npm:is-finalizationregistry","npm:is-generator-function","npm:is-glob","npm:is-map","npm:is-negative-zero","npm:is-number","npm:is-number-object","npm:is-regex","npm:is-set","npm:is-shared-array-buffer","npm:is-string","npm:is-symbol","npm:is-typed-array","npm:is-weakmap","npm:is-weakref","npm:is-weakset","npm:isarray","npm:isexe","npm:iterator.prototype","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@1.0.2","npm:jsx-ast-utils","npm:keyv","npm:language-subtag-registry","npm:language-tags","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:loose-envify","npm:magic-string","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:minimist@1.2.8","npm:minipass","npm:minizlib","npm:mkdirp","npm:ms@2.1.3","npm:nanoid","npm:napi-postinstall","npm:natural-compare","npm:next","npm:object-assign","npm:object-inspect","npm:object-keys","npm:object.assign","npm:object.entries","npm:object.fromentries","npm:object.groupby","npm:object.values","npm:optionator","npm:own-keys","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:path-parse","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:possible-typed-array-names","npm:postcss@8.4.31","npm:postcss@8.5.6","npm:prelude-ls","npm:prop-types","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-is@16.13.1","npm:reflect.getprototypeof","npm:regexp.prototype.flags","npm:resolve-from@4.0.0","npm:resolve-pkg-maps","npm:resolve@1.22.10","npm:resolve@2.0.0-next.5","npm:reusify","npm:run-parallel","npm:safe-array-concat","npm:safe-push-apply","npm:safe-regex-test","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:set-function-length","npm:set-function-name","npm:set-proto","npm:sharp","npm:shebang-command","npm:shebang-regex","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-swizzle","npm:source-map-js","npm:stable-hash","npm:stable-hash-x","npm:stop-iteration-iterator","npm:streamsearch","npm:string.prototype.includes","npm:string.prototype.matchall","npm:string.prototype.repeat","npm:string.prototype.trim","npm:string.prototype.trimend","npm:string.prototype.trimstart","npm:strip-bom@3.0.0","npm:strip-json-comments","npm:styled-jsx","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:supports-preserve-symlinks-flag","npm:tailwindcss","npm:tapable","npm:tar","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:tsconfig-paths@3.15.0","npm:tslib","npm:type-check","npm:typed-array-buffer","npm:typed-array-byte-length","npm:typed-array-byte-offset","npm:typed-array-length","npm:typescript","npm:unbox-primitive","npm:undici-types@6.21.0","npm:unrs-resolver","npm:uri-js","npm:which-boxed-primitive","npm:which-builtin-type","npm:which-collection","npm:which-typed-array","npm:which@2.0.2","npm:word-wrap","npm:yallist@5.0.0","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/marketing:dev":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/marketing:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/marketing:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/marketing:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@alloc/quick-lru","npm:@ampproject/remapping","npm:@emnapi/core","npm:@emnapi/runtime","npm:@emnapi/wasi-threads","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@img/sharp-darwin-arm64","npm:@img/sharp-darwin-x64","npm:@img/sharp-libvips-darwin-arm64","npm:@img/sharp-libvips-darwin-x64","npm:@img/sharp-libvips-linux-arm","npm:@img/sharp-libvips-linux-arm64","npm:@img/sharp-libvips-linux-ppc64","npm:@img/sharp-libvips-linux-s390x","npm:@img/sharp-libvips-linux-x64","npm:@img/sharp-libvips-linuxmusl-arm64","npm:@img/sharp-libvips-linuxmusl-x64","npm:@img/sharp-linux-arm","npm:@img/sharp-linux-arm64","npm:@img/sharp-linux-s390x","npm:@img/sharp-linux-x64","npm:@img/sharp-linuxmusl-arm64","npm:@img/sharp-linuxmusl-x64","npm:@img/sharp-wasm32","npm:@img/sharp-win32-arm64","npm:@img/sharp-win32-ia32","npm:@img/sharp-win32-x64","npm:@isaacs/fs-minipass","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@napi-rs/wasm-runtime@0.2.11","npm:@next/env","npm:@next/eslint-plugin-next","npm:@next/swc-darwin-arm64","npm:@next/swc-darwin-x64","npm:@next/swc-linux-arm64-gnu","npm:@next/swc-linux-arm64-musl","npm:@next/swc-linux-x64-gnu","npm:@next/swc-linux-x64-musl","npm:@next/swc-win32-arm64-msvc","npm:@next/swc-win32-x64-msvc","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@nolyfill/is-core-module","npm:@rtsao/scc","npm:@rushstack/eslint-patch","npm:@swc/counter","npm:@swc/helpers","npm:@tailwindcss/node","npm:@tailwindcss/oxide","npm:@tailwindcss/oxide-android-arm64","npm:@tailwindcss/oxide-darwin-arm64","npm:@tailwindcss/oxide-darwin-x64","npm:@tailwindcss/oxide-freebsd-x64","npm:@tailwindcss/oxide-linux-arm-gnueabihf","npm:@tailwindcss/oxide-linux-arm64-gnu","npm:@tailwindcss/oxide-linux-arm64-musl","npm:@tailwindcss/oxide-linux-x64-gnu","npm:@tailwindcss/oxide-linux-x64-musl","npm:@tailwindcss/oxide-wasm32-wasi","npm:@tailwindcss/oxide-win32-arm64-msvc","npm:@tailwindcss/oxide-win32-x64-msvc","npm:@tailwindcss/postcss","npm:@tybys/wasm-util","npm:@types/estree","npm:@types/json-schema","npm:@types/json5","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@unrs/resolver-binding-android-arm-eabi","npm:@unrs/resolver-binding-android-arm64","npm:@unrs/resolver-binding-darwin-arm64","npm:@unrs/resolver-binding-darwin-x64","npm:@unrs/resolver-binding-freebsd-x64","npm:@unrs/resolver-binding-linux-arm-gnueabihf","npm:@unrs/resolver-binding-linux-arm-musleabihf","npm:@unrs/resolver-binding-linux-arm64-gnu","npm:@unrs/resolver-binding-linux-arm64-musl","npm:@unrs/resolver-binding-linux-ppc64-gnu","npm:@unrs/resolver-binding-linux-riscv64-gnu","npm:@unrs/resolver-binding-linux-riscv64-musl","npm:@unrs/resolver-binding-linux-s390x-gnu","npm:@unrs/resolver-binding-linux-x64-gnu","npm:@unrs/resolver-binding-linux-x64-musl","npm:@unrs/resolver-binding-wasm32-wasi","npm:@unrs/resolver-binding-win32-arm64-msvc","npm:@unrs/resolver-binding-win32-ia32-msvc","npm:@unrs/resolver-binding-win32-x64-msvc","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:aria-query","npm:array-buffer-byte-length","npm:array-includes","npm:array.prototype.findlast","npm:array.prototype.findlastindex","npm:array.prototype.flat","npm:array.prototype.flatmap","npm:array.prototype.tosorted","npm:arraybuffer.prototype.slice","npm:ast-types-flow","npm:async-function","npm:available-typed-arrays","npm:axe-core","npm:axobject-query","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:busboy","npm:call-bind","npm:call-bind-apply-helpers","npm:call-bound","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:chownr","npm:client-only","npm:color","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:color-string","npm:concat-map","npm:cross-spawn","npm:csstype","npm:damerau-levenshtein","npm:data-view-buffer","npm:data-view-byte-length","npm:data-view-byte-offset","npm:debug@3.2.7","npm:debug@4.4.1","npm:deep-is","npm:define-data-property","npm:define-properties","npm:detect-libc","npm:doctrine","npm:dunder-proto","npm:emoji-regex@9.2.2","npm:enhanced-resolve","npm:es-abstract","npm:es-define-property","npm:es-errors","npm:es-iterator-helpers","npm:es-object-atoms","npm:es-set-tostringtag","npm:es-shim-unscopables","npm:es-to-primitive","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-config-next","npm:eslint-import-context","npm:eslint-import-resolver-node","npm:eslint-import-resolver-typescript","npm:eslint-import-resolver-typescript@3.10.1","npm:eslint-module-utils","npm:eslint-plugin-import","npm:eslint-plugin-jsx-a11y","npm:eslint-plugin-react","npm:eslint-plugin-react-hooks","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.1","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:for-each","npm:function-bind","npm:function.prototype.name","npm:functions-have-names","npm:get-intrinsic","npm:get-proto","npm:get-symbol-description","npm:get-tsconfig","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals@14.0.0","npm:globalthis","npm:gopd","npm:graceful-fs","npm:graphemer","npm:has-bigints","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-property-descriptors","npm:has-proto","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:internal-slot","npm:is-array-buffer","npm:is-arrayish@0.3.2","npm:is-async-function","npm:is-bigint","npm:is-boolean-object","npm:is-bun-module","npm:is-callable","npm:is-core-module","npm:is-data-view","npm:is-date-object","npm:is-extglob","npm:is-finalizationregistry","npm:is-generator-function","npm:is-glob","npm:is-map","npm:is-negative-zero","npm:is-number","npm:is-number-object","npm:is-regex","npm:is-set","npm:is-shared-array-buffer","npm:is-string","npm:is-symbol","npm:is-typed-array","npm:is-weakmap","npm:is-weakref","npm:is-weakset","npm:isarray","npm:isexe","npm:iterator.prototype","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@1.0.2","npm:jsx-ast-utils","npm:keyv","npm:language-subtag-registry","npm:language-tags","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:loose-envify","npm:magic-string","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:minimist@1.2.8","npm:minipass","npm:minizlib","npm:mkdirp","npm:ms@2.1.3","npm:nanoid","npm:napi-postinstall","npm:natural-compare","npm:next","npm:object-assign","npm:object-inspect","npm:object-keys","npm:object.assign","npm:object.entries","npm:object.fromentries","npm:object.groupby","npm:object.values","npm:optionator","npm:own-keys","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:path-parse","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:possible-typed-array-names","npm:postcss@8.4.31","npm:postcss@8.5.6","npm:prelude-ls","npm:prop-types","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-is@16.13.1","npm:reflect.getprototypeof","npm:regexp.prototype.flags","npm:resolve-from@4.0.0","npm:resolve-pkg-maps","npm:resolve@1.22.10","npm:resolve@2.0.0-next.5","npm:reusify","npm:run-parallel","npm:safe-array-concat","npm:safe-push-apply","npm:safe-regex-test","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:set-function-length","npm:set-function-name","npm:set-proto","npm:sharp","npm:shebang-command","npm:shebang-regex","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-swizzle","npm:source-map-js","npm:stable-hash","npm:stable-hash-x","npm:stop-iteration-iterator","npm:streamsearch","npm:string.prototype.includes","npm:string.prototype.matchall","npm:string.prototype.repeat","npm:string.prototype.trim","npm:string.prototype.trimend","npm:string.prototype.trimstart","npm:strip-bom@3.0.0","npm:strip-json-comments","npm:styled-jsx","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:supports-preserve-symlinks-flag","npm:tailwindcss","npm:tapable","npm:tar","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:tsconfig-paths@3.15.0","npm:tslib","npm:type-check","npm:typed-array-buffer","npm:typed-array-byte-length","npm:typed-array-byte-offset","npm:typed-array-length","npm:typescript","npm:unbox-primitive","npm:undici-types@6.21.0","npm:unrs-resolver","npm:uri-js","npm:which-boxed-primitive","npm:which-builtin-type","npm:which-collection","npm:which-typed-array","npm:which@2.0.2","npm:word-wrap","npm:yallist@5.0.0","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/marketing:build":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/marketing:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/marketing:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/ui:ProjectConfiguration","@mobility-network/marketing:TsConfig","@mobility-network/shared:TsConfig","@mobility-network/ui:TsConfig","npm:@alloc/quick-lru","npm:@ampproject/remapping","npm:@emnapi/core","npm:@emnapi/runtime","npm:@emnapi/wasi-threads","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@img/sharp-darwin-arm64","npm:@img/sharp-darwin-x64","npm:@img/sharp-libvips-darwin-arm64","npm:@img/sharp-libvips-darwin-x64","npm:@img/sharp-libvips-linux-arm","npm:@img/sharp-libvips-linux-arm64","npm:@img/sharp-libvips-linux-ppc64","npm:@img/sharp-libvips-linux-s390x","npm:@img/sharp-libvips-linux-x64","npm:@img/sharp-libvips-linuxmusl-arm64","npm:@img/sharp-libvips-linuxmusl-x64","npm:@img/sharp-linux-arm","npm:@img/sharp-linux-arm64","npm:@img/sharp-linux-s390x","npm:@img/sharp-linux-x64","npm:@img/sharp-linuxmusl-arm64","npm:@img/sharp-linuxmusl-x64","npm:@img/sharp-wasm32","npm:@img/sharp-win32-arm64","npm:@img/sharp-win32-ia32","npm:@img/sharp-win32-x64","npm:@isaacs/fs-minipass","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@napi-rs/wasm-runtime@0.2.11","npm:@next/env","npm:@next/eslint-plugin-next","npm:@next/swc-darwin-arm64","npm:@next/swc-darwin-x64","npm:@next/swc-linux-arm64-gnu","npm:@next/swc-linux-arm64-musl","npm:@next/swc-linux-x64-gnu","npm:@next/swc-linux-x64-musl","npm:@next/swc-win32-arm64-msvc","npm:@next/swc-win32-x64-msvc","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@nolyfill/is-core-module","npm:@rtsao/scc","npm:@rushstack/eslint-patch","npm:@swc/counter","npm:@swc/helpers","npm:@tailwindcss/node","npm:@tailwindcss/oxide","npm:@tailwindcss/oxide-android-arm64","npm:@tailwindcss/oxide-darwin-arm64","npm:@tailwindcss/oxide-darwin-x64","npm:@tailwindcss/oxide-freebsd-x64","npm:@tailwindcss/oxide-linux-arm-gnueabihf","npm:@tailwindcss/oxide-linux-arm64-gnu","npm:@tailwindcss/oxide-linux-arm64-musl","npm:@tailwindcss/oxide-linux-x64-gnu","npm:@tailwindcss/oxide-linux-x64-musl","npm:@tailwindcss/oxide-wasm32-wasi","npm:@tailwindcss/oxide-win32-arm64-msvc","npm:@tailwindcss/oxide-win32-x64-msvc","npm:@tailwindcss/postcss","npm:@tybys/wasm-util","npm:@types/estree","npm:@types/json-schema","npm:@types/json5","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@unrs/resolver-binding-android-arm-eabi","npm:@unrs/resolver-binding-android-arm64","npm:@unrs/resolver-binding-darwin-arm64","npm:@unrs/resolver-binding-darwin-x64","npm:@unrs/resolver-binding-freebsd-x64","npm:@unrs/resolver-binding-linux-arm-gnueabihf","npm:@unrs/resolver-binding-linux-arm-musleabihf","npm:@unrs/resolver-binding-linux-arm64-gnu","npm:@unrs/resolver-binding-linux-arm64-musl","npm:@unrs/resolver-binding-linux-ppc64-gnu","npm:@unrs/resolver-binding-linux-riscv64-gnu","npm:@unrs/resolver-binding-linux-riscv64-musl","npm:@unrs/resolver-binding-linux-s390x-gnu","npm:@unrs/resolver-binding-linux-x64-gnu","npm:@unrs/resolver-binding-linux-x64-musl","npm:@unrs/resolver-binding-wasm32-wasi","npm:@unrs/resolver-binding-win32-arm64-msvc","npm:@unrs/resolver-binding-win32-ia32-msvc","npm:@unrs/resolver-binding-win32-x64-msvc","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:aria-query","npm:array-buffer-byte-length","npm:array-includes","npm:array.prototype.findlast","npm:array.prototype.findlastindex","npm:array.prototype.flat","npm:array.prototype.flatmap","npm:array.prototype.tosorted","npm:arraybuffer.prototype.slice","npm:ast-types-flow","npm:async-function","npm:available-typed-arrays","npm:axe-core","npm:axobject-query","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:busboy","npm:call-bind","npm:call-bind-apply-helpers","npm:call-bound","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:chownr","npm:client-only","npm:color","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:color-string","npm:concat-map","npm:cross-spawn","npm:csstype","npm:damerau-levenshtein","npm:data-view-buffer","npm:data-view-byte-length","npm:data-view-byte-offset","npm:debug@3.2.7","npm:debug@4.4.1","npm:deep-is","npm:define-data-property","npm:define-properties","npm:detect-libc","npm:doctrine","npm:dunder-proto","npm:emoji-regex@9.2.2","npm:enhanced-resolve","npm:es-abstract","npm:es-define-property","npm:es-errors","npm:es-iterator-helpers","npm:es-object-atoms","npm:es-set-tostringtag","npm:es-shim-unscopables","npm:es-to-primitive","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-config-next","npm:eslint-import-context","npm:eslint-import-resolver-node","npm:eslint-import-resolver-typescript","npm:eslint-import-resolver-typescript@3.10.1","npm:eslint-module-utils","npm:eslint-plugin-import","npm:eslint-plugin-jsx-a11y","npm:eslint-plugin-react","npm:eslint-plugin-react-hooks","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.1","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:for-each","npm:function-bind","npm:function.prototype.name","npm:functions-have-names","npm:get-intrinsic","npm:get-proto","npm:get-symbol-description","npm:get-tsconfig","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals@14.0.0","npm:globalthis","npm:gopd","npm:graceful-fs","npm:graphemer","npm:has-bigints","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-property-descriptors","npm:has-proto","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:internal-slot","npm:is-array-buffer","npm:is-arrayish@0.3.2","npm:is-async-function","npm:is-bigint","npm:is-boolean-object","npm:is-bun-module","npm:is-callable","npm:is-core-module","npm:is-data-view","npm:is-date-object","npm:is-extglob","npm:is-finalizationregistry","npm:is-generator-function","npm:is-glob","npm:is-map","npm:is-negative-zero","npm:is-number","npm:is-number-object","npm:is-regex","npm:is-set","npm:is-shared-array-buffer","npm:is-string","npm:is-symbol","npm:is-typed-array","npm:is-weakmap","npm:is-weakref","npm:is-weakset","npm:isarray","npm:isexe","npm:iterator.prototype","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@1.0.2","npm:jsx-ast-utils","npm:keyv","npm:language-subtag-registry","npm:language-tags","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:loose-envify","npm:magic-string","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:minimist@1.2.8","npm:minipass","npm:minizlib","npm:mkdirp","npm:ms@2.1.3","npm:nanoid","npm:napi-postinstall","npm:natural-compare","npm:next","npm:object-assign","npm:object-inspect","npm:object-keys","npm:object.assign","npm:object.entries","npm:object.fromentries","npm:object.groupby","npm:object.values","npm:optionator","npm:own-keys","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:path-parse","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:possible-typed-array-names","npm:postcss@8.4.31","npm:postcss@8.5.6","npm:prelude-ls","npm:prop-types","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-is@16.13.1","npm:reflect.getprototypeof","npm:regexp.prototype.flags","npm:resolve-from@4.0.0","npm:resolve-pkg-maps","npm:resolve@1.22.10","npm:resolve@2.0.0-next.5","npm:reusify","npm:run-parallel","npm:safe-array-concat","npm:safe-push-apply","npm:safe-regex-test","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:set-function-length","npm:set-function-name","npm:set-proto","npm:sharp","npm:shebang-command","npm:shebang-regex","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-swizzle","npm:source-map-js","npm:stable-hash","npm:stable-hash-x","npm:stop-iteration-iterator","npm:streamsearch","npm:string.prototype.includes","npm:string.prototype.matchall","npm:string.prototype.repeat","npm:string.prototype.trim","npm:string.prototype.trimend","npm:string.prototype.trimstart","npm:strip-bom@3.0.0","npm:strip-json-comments","npm:styled-jsx","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:supports-preserve-symlinks-flag","npm:tailwindcss","npm:tapable","npm:tar","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:tsconfig-paths@3.15.0","npm:tslib","npm:type-check","npm:typed-array-buffer","npm:typed-array-byte-length","npm:typed-array-byte-offset","npm:typed-array-length","npm:typescript","npm:unbox-primitive","npm:undici-types@6.21.0","npm:unrs-resolver","npm:uri-js","npm:which-boxed-primitive","npm:which-builtin-type","npm:which-collection","npm:which-typed-array","npm:which@2.0.2","npm:word-wrap","npm:yallist@5.0.0","npm:yocto-queue","AllExternalDependencies"],"@mobility-network/ui:dev":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/ui:ProjectConfiguration","@mobility-network/ui:TsConfig","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript","AllExternalDependencies"],"@mobility-network/ui:clean":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/ui:ProjectConfiguration","@mobility-network/ui:TsConfig","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript","AllExternalDependencies"],"@mobility-network/ui:lint":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/ui:ProjectConfiguration","@mobility-network/ui:TsConfig","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript","AllExternalDependencies"],"@mobility-network/ui:type-check":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/ui:{projectRoot}/**/*","@mobility-network/ui:ProjectConfiguration","@mobility-network/ui:TsConfig","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript","AllExternalDependencies"],"@mobility-network/api:start":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/api:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/api:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/api:TsConfig","@mobility-network/shared:TsConfig","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn","AllExternalDependencies"],"@mobility-network/api:lint":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/api:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/api:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/api:TsConfig","@mobility-network/shared:TsConfig","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn","AllExternalDependencies"],"@mobility-network/api:lint:fix":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/api:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/api:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/api:TsConfig","@mobility-network/shared:TsConfig","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn","AllExternalDependencies"],"@mobility-network/api:db:generate":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/api:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/api:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/api:TsConfig","@mobility-network/shared:TsConfig","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn","AllExternalDependencies"],"@mobility-network/api:db:migrate":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/api:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/api:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/api:TsConfig","@mobility-network/shared:TsConfig","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn","AllExternalDependencies"],"@mobility-network/api:dev":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/api:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/api:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/api:TsConfig","@mobility-network/shared:TsConfig","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn","AllExternalDependencies"],"@mobility-network/api:build":["workspace:[{workspaceRoot}/nx.json,{workspaceRoot}/.gitignore,{workspaceRoot}/.nxignore]","env:NX_CLOUD_ENCRYPTION_KEY","@mobility-network/api:{projectRoot}/**/*","@mobility-network/shared:{projectRoot}/**/*","@mobility-network/api:ProjectConfiguration","@mobility-network/shared:ProjectConfiguration","@mobility-network/api:TsConfig","@mobility-network/shared:TsConfig","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn","AllExternalDependencies"]}};
    window.expandedTaskInputsResponse = {"@mobility-network/shared:dev":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:typescript"]},"@mobility-network/shared:clean":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:typescript"]},"@mobility-network/shared:build":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:typescript"]},"@mobility-network/shared:lint":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:typescript"]},"@mobility-network/shared:type-check":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:typescript"]},"@mobility-network/dashboard:lint:fix":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/dashboard":["apps/dashboard/.gitignore","apps/dashboard/README.md","apps/dashboard/eslint.config.js","apps/dashboard/index.html","apps/dashboard/package.json","apps/dashboard/project.json","apps/dashboard/public/vite.svg","apps/dashboard/src/App.css","apps/dashboard/src/App.tsx","apps/dashboard/src/assets/react.svg","apps/dashboard/src/index.css","apps/dashboard/src/main.tsx","apps/dashboard/src/vite-env.d.ts","apps/dashboard/tsconfig.app.json","apps/dashboard/tsconfig.json","apps/dashboard/tsconfig.node.json","apps/dashboard/vite.config.ts"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue"]},"@mobility-network/dashboard:dev":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/dashboard":["apps/dashboard/.gitignore","apps/dashboard/README.md","apps/dashboard/eslint.config.js","apps/dashboard/index.html","apps/dashboard/package.json","apps/dashboard/project.json","apps/dashboard/public/vite.svg","apps/dashboard/src/App.css","apps/dashboard/src/App.tsx","apps/dashboard/src/assets/react.svg","apps/dashboard/src/index.css","apps/dashboard/src/main.tsx","apps/dashboard/src/vite-env.d.ts","apps/dashboard/tsconfig.app.json","apps/dashboard/tsconfig.json","apps/dashboard/tsconfig.node.json","apps/dashboard/vite.config.ts"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue"]},"@mobility-network/ui:build":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript"]},"@mobility-network/dashboard:build":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/dashboard":["apps/dashboard/.gitignore","apps/dashboard/README.md","apps/dashboard/eslint.config.js","apps/dashboard/index.html","apps/dashboard/package.json","apps/dashboard/project.json","apps/dashboard/public/vite.svg","apps/dashboard/src/App.css","apps/dashboard/src/App.tsx","apps/dashboard/src/assets/react.svg","apps/dashboard/src/index.css","apps/dashboard/src/main.tsx","apps/dashboard/src/vite-env.d.ts","apps/dashboard/tsconfig.app.json","apps/dashboard/tsconfig.json","apps/dashboard/tsconfig.node.json","apps/dashboard/vite.config.ts"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue"]},"@mobility-network/dashboard:preview":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/dashboard":["apps/dashboard/.gitignore","apps/dashboard/README.md","apps/dashboard/eslint.config.js","apps/dashboard/index.html","apps/dashboard/package.json","apps/dashboard/project.json","apps/dashboard/public/vite.svg","apps/dashboard/src/App.css","apps/dashboard/src/App.tsx","apps/dashboard/src/assets/react.svg","apps/dashboard/src/index.css","apps/dashboard/src/main.tsx","apps/dashboard/src/vite-env.d.ts","apps/dashboard/tsconfig.app.json","apps/dashboard/tsconfig.json","apps/dashboard/tsconfig.node.json","apps/dashboard/vite.config.ts"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue"]},"@mobility-network/dashboard:lint":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/dashboard":["apps/dashboard/.gitignore","apps/dashboard/README.md","apps/dashboard/eslint.config.js","apps/dashboard/index.html","apps/dashboard/package.json","apps/dashboard/project.json","apps/dashboard/public/vite.svg","apps/dashboard/src/App.css","apps/dashboard/src/App.tsx","apps/dashboard/src/assets/react.svg","apps/dashboard/src/index.css","apps/dashboard/src/main.tsx","apps/dashboard/src/vite-env.d.ts","apps/dashboard/tsconfig.app.json","apps/dashboard/tsconfig.json","apps/dashboard/tsconfig.node.json","apps/dashboard/vite.config.ts"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@ampproject/remapping","npm:@babel/code-frame","npm:@babel/compat-data","npm:@babel/core","npm:@babel/generator","npm:@babel/helper-compilation-targets","npm:@babel/helper-module-imports","npm:@babel/helper-module-transforms","npm:@babel/helper-plugin-utils","npm:@babel/helper-string-parser","npm:@babel/helper-validator-identifier","npm:@babel/helper-validator-option","npm:@babel/helpers","npm:@babel/parser","npm:@babel/plugin-transform-react-jsx-self","npm:@babel/plugin-transform-react-jsx-source","npm:@babel/template","npm:@babel/traverse","npm:@babel/types","npm:@esbuild/aix-ppc64","npm:@esbuild/android-arm","npm:@esbuild/android-arm64","npm:@esbuild/android-x64","npm:@esbuild/darwin-arm64","npm:@esbuild/darwin-x64","npm:@esbuild/freebsd-arm64","npm:@esbuild/freebsd-x64","npm:@esbuild/linux-arm","npm:@esbuild/linux-arm64","npm:@esbuild/linux-ia32","npm:@esbuild/linux-loong64","npm:@esbuild/linux-mips64el","npm:@esbuild/linux-ppc64","npm:@esbuild/linux-riscv64","npm:@esbuild/linux-s390x","npm:@esbuild/linux-x64","npm:@esbuild/netbsd-arm64","npm:@esbuild/netbsd-x64","npm:@esbuild/openbsd-arm64","npm:@esbuild/openbsd-x64","npm:@esbuild/sunos-x64","npm:@esbuild/win32-arm64","npm:@esbuild/win32-ia32","npm:@esbuild/win32-x64","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@rolldown/pluginutils","npm:@rollup/rollup-android-arm-eabi","npm:@rollup/rollup-android-arm64","npm:@rollup/rollup-darwin-arm64","npm:@rollup/rollup-darwin-x64","npm:@rollup/rollup-freebsd-arm64","npm:@rollup/rollup-freebsd-x64","npm:@rollup/rollup-linux-arm-gnueabihf","npm:@rollup/rollup-linux-arm-musleabihf","npm:@rollup/rollup-linux-arm64-gnu","npm:@rollup/rollup-linux-arm64-musl","npm:@rollup/rollup-linux-loongarch64-gnu","npm:@rollup/rollup-linux-powerpc64le-gnu","npm:@rollup/rollup-linux-riscv64-gnu","npm:@rollup/rollup-linux-riscv64-musl","npm:@rollup/rollup-linux-s390x-gnu","npm:@rollup/rollup-linux-x64-gnu","npm:@rollup/rollup-linux-x64-musl","npm:@rollup/rollup-win32-arm64-msvc","npm:@rollup/rollup-win32-ia32-msvc","npm:@rollup/rollup-win32-x64-msvc","npm:@tanstack/query-core","npm:@tanstack/react-query","npm:@types/babel__core","npm:@types/babel__generator","npm:@types/babel__template","npm:@types/babel__traverse","npm:@types/estree","npm:@types/json-schema","npm:@types/node@24.0.3","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@vitejs/plugin-react","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:asynckit","npm:axios","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:browserslist","npm:call-bind-apply-helpers","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:combined-stream","npm:concat-map","npm:convert-source-map","npm:cross-spawn","npm:csstype","npm:debug@4.4.1","npm:deep-is","npm:delayed-stream","npm:detect-libc","npm:dunder-proto","npm:electron-to-chromium","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:es-set-tostringtag","npm:esbuild","npm:escalade","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-plugin-react-hooks","npm:eslint-plugin-react-refresh","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:follow-redirects","npm:form-data","npm:fsevents","npm:function-bind","npm:gensync","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals","npm:globals@11.12.0","npm:globals@14.0.0","npm:gopd","npm:graphemer","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:is-extglob","npm:is-glob","npm:is-number","npm:isexe","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:jsesc","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@2.2.3","npm:keyv","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:lru-cache","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:mime-db@1.52.0","npm:mime-types@2.1.35","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:ms@2.1.3","npm:nanoid","npm:natural-compare","npm:node-releases","npm:optionator","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:postcss@8.5.6","npm:prelude-ls","npm:proxy-from-env","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-refresh","npm:resolve-from@4.0.0","npm:reusify","npm:rollup","npm:run-parallel","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:shebang-command","npm:shebang-regex","npm:source-map-js","npm:strip-json-comments","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:type-check","npm:typescript","npm:typescript-eslint","npm:undici-types@7.8.0","npm:update-browserslist-db","npm:uri-js","npm:vite","npm:which@2.0.2","npm:word-wrap","npm:yallist@3.1.1","npm:yaml","npm:yocto-queue"]},"@mobility-network/marketing:start":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/marketing":["apps/marketing/.gitignore","apps/marketing/README.md","apps/marketing/eslint.config.mjs","apps/marketing/next.config.ts","apps/marketing/package.json","apps/marketing/postcss.config.mjs","apps/marketing/project.json","apps/marketing/public/file.svg","apps/marketing/public/globe.svg","apps/marketing/public/next.svg","apps/marketing/public/vercel.svg","apps/marketing/public/window.svg","apps/marketing/src/app/favicon.ico","apps/marketing/src/app/globals.css","apps/marketing/src/app/layout.tsx","apps/marketing/src/app/page.tsx","apps/marketing/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@alloc/quick-lru","npm:@ampproject/remapping","npm:@emnapi/core","npm:@emnapi/runtime","npm:@emnapi/wasi-threads","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@img/sharp-darwin-arm64","npm:@img/sharp-darwin-x64","npm:@img/sharp-libvips-darwin-arm64","npm:@img/sharp-libvips-darwin-x64","npm:@img/sharp-libvips-linux-arm","npm:@img/sharp-libvips-linux-arm64","npm:@img/sharp-libvips-linux-ppc64","npm:@img/sharp-libvips-linux-s390x","npm:@img/sharp-libvips-linux-x64","npm:@img/sharp-libvips-linuxmusl-arm64","npm:@img/sharp-libvips-linuxmusl-x64","npm:@img/sharp-linux-arm","npm:@img/sharp-linux-arm64","npm:@img/sharp-linux-s390x","npm:@img/sharp-linux-x64","npm:@img/sharp-linuxmusl-arm64","npm:@img/sharp-linuxmusl-x64","npm:@img/sharp-wasm32","npm:@img/sharp-win32-arm64","npm:@img/sharp-win32-ia32","npm:@img/sharp-win32-x64","npm:@isaacs/fs-minipass","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@napi-rs/wasm-runtime@0.2.11","npm:@next/env","npm:@next/eslint-plugin-next","npm:@next/swc-darwin-arm64","npm:@next/swc-darwin-x64","npm:@next/swc-linux-arm64-gnu","npm:@next/swc-linux-arm64-musl","npm:@next/swc-linux-x64-gnu","npm:@next/swc-linux-x64-musl","npm:@next/swc-win32-arm64-msvc","npm:@next/swc-win32-x64-msvc","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@nolyfill/is-core-module","npm:@rtsao/scc","npm:@rushstack/eslint-patch","npm:@swc/counter","npm:@swc/helpers","npm:@tailwindcss/node","npm:@tailwindcss/oxide","npm:@tailwindcss/oxide-android-arm64","npm:@tailwindcss/oxide-darwin-arm64","npm:@tailwindcss/oxide-darwin-x64","npm:@tailwindcss/oxide-freebsd-x64","npm:@tailwindcss/oxide-linux-arm-gnueabihf","npm:@tailwindcss/oxide-linux-arm64-gnu","npm:@tailwindcss/oxide-linux-arm64-musl","npm:@tailwindcss/oxide-linux-x64-gnu","npm:@tailwindcss/oxide-linux-x64-musl","npm:@tailwindcss/oxide-wasm32-wasi","npm:@tailwindcss/oxide-win32-arm64-msvc","npm:@tailwindcss/oxide-win32-x64-msvc","npm:@tailwindcss/postcss","npm:@tybys/wasm-util","npm:@types/estree","npm:@types/json-schema","npm:@types/json5","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@unrs/resolver-binding-android-arm-eabi","npm:@unrs/resolver-binding-android-arm64","npm:@unrs/resolver-binding-darwin-arm64","npm:@unrs/resolver-binding-darwin-x64","npm:@unrs/resolver-binding-freebsd-x64","npm:@unrs/resolver-binding-linux-arm-gnueabihf","npm:@unrs/resolver-binding-linux-arm-musleabihf","npm:@unrs/resolver-binding-linux-arm64-gnu","npm:@unrs/resolver-binding-linux-arm64-musl","npm:@unrs/resolver-binding-linux-ppc64-gnu","npm:@unrs/resolver-binding-linux-riscv64-gnu","npm:@unrs/resolver-binding-linux-riscv64-musl","npm:@unrs/resolver-binding-linux-s390x-gnu","npm:@unrs/resolver-binding-linux-x64-gnu","npm:@unrs/resolver-binding-linux-x64-musl","npm:@unrs/resolver-binding-wasm32-wasi","npm:@unrs/resolver-binding-win32-arm64-msvc","npm:@unrs/resolver-binding-win32-ia32-msvc","npm:@unrs/resolver-binding-win32-x64-msvc","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:aria-query","npm:array-buffer-byte-length","npm:array-includes","npm:array.prototype.findlast","npm:array.prototype.findlastindex","npm:array.prototype.flat","npm:array.prototype.flatmap","npm:array.prototype.tosorted","npm:arraybuffer.prototype.slice","npm:ast-types-flow","npm:async-function","npm:available-typed-arrays","npm:axe-core","npm:axobject-query","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:busboy","npm:call-bind","npm:call-bind-apply-helpers","npm:call-bound","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:chownr","npm:client-only","npm:color","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:color-string","npm:concat-map","npm:cross-spawn","npm:csstype","npm:damerau-levenshtein","npm:data-view-buffer","npm:data-view-byte-length","npm:data-view-byte-offset","npm:debug@3.2.7","npm:debug@4.4.1","npm:deep-is","npm:define-data-property","npm:define-properties","npm:detect-libc","npm:doctrine","npm:dunder-proto","npm:emoji-regex@9.2.2","npm:enhanced-resolve","npm:es-abstract","npm:es-define-property","npm:es-errors","npm:es-iterator-helpers","npm:es-object-atoms","npm:es-set-tostringtag","npm:es-shim-unscopables","npm:es-to-primitive","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-config-next","npm:eslint-import-context","npm:eslint-import-resolver-node","npm:eslint-import-resolver-typescript","npm:eslint-import-resolver-typescript@3.10.1","npm:eslint-module-utils","npm:eslint-plugin-import","npm:eslint-plugin-jsx-a11y","npm:eslint-plugin-react","npm:eslint-plugin-react-hooks","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.1","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:for-each","npm:function-bind","npm:function.prototype.name","npm:functions-have-names","npm:get-intrinsic","npm:get-proto","npm:get-symbol-description","npm:get-tsconfig","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals@14.0.0","npm:globalthis","npm:gopd","npm:graceful-fs","npm:graphemer","npm:has-bigints","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-property-descriptors","npm:has-proto","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:internal-slot","npm:is-array-buffer","npm:is-arrayish@0.3.2","npm:is-async-function","npm:is-bigint","npm:is-boolean-object","npm:is-bun-module","npm:is-callable","npm:is-core-module","npm:is-data-view","npm:is-date-object","npm:is-extglob","npm:is-finalizationregistry","npm:is-generator-function","npm:is-glob","npm:is-map","npm:is-negative-zero","npm:is-number","npm:is-number-object","npm:is-regex","npm:is-set","npm:is-shared-array-buffer","npm:is-string","npm:is-symbol","npm:is-typed-array","npm:is-weakmap","npm:is-weakref","npm:is-weakset","npm:isarray","npm:isexe","npm:iterator.prototype","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@1.0.2","npm:jsx-ast-utils","npm:keyv","npm:language-subtag-registry","npm:language-tags","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:loose-envify","npm:magic-string","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:minimist@1.2.8","npm:minipass","npm:minizlib","npm:mkdirp","npm:ms@2.1.3","npm:nanoid","npm:napi-postinstall","npm:natural-compare","npm:next","npm:object-assign","npm:object-inspect","npm:object-keys","npm:object.assign","npm:object.entries","npm:object.fromentries","npm:object.groupby","npm:object.values","npm:optionator","npm:own-keys","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:path-parse","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:possible-typed-array-names","npm:postcss@8.4.31","npm:postcss@8.5.6","npm:prelude-ls","npm:prop-types","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-is@16.13.1","npm:reflect.getprototypeof","npm:regexp.prototype.flags","npm:resolve-from@4.0.0","npm:resolve-pkg-maps","npm:resolve@1.22.10","npm:resolve@2.0.0-next.5","npm:reusify","npm:run-parallel","npm:safe-array-concat","npm:safe-push-apply","npm:safe-regex-test","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:set-function-length","npm:set-function-name","npm:set-proto","npm:sharp","npm:shebang-command","npm:shebang-regex","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-swizzle","npm:source-map-js","npm:stable-hash","npm:stable-hash-x","npm:stop-iteration-iterator","npm:streamsearch","npm:string.prototype.includes","npm:string.prototype.matchall","npm:string.prototype.repeat","npm:string.prototype.trim","npm:string.prototype.trimend","npm:string.prototype.trimstart","npm:strip-bom@3.0.0","npm:strip-json-comments","npm:styled-jsx","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:supports-preserve-symlinks-flag","npm:tailwindcss","npm:tapable","npm:tar","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:tsconfig-paths@3.15.0","npm:tslib","npm:type-check","npm:typed-array-buffer","npm:typed-array-byte-length","npm:typed-array-byte-offset","npm:typed-array-length","npm:typescript","npm:unbox-primitive","npm:undici-types@6.21.0","npm:unrs-resolver","npm:uri-js","npm:which-boxed-primitive","npm:which-builtin-type","npm:which-collection","npm:which-typed-array","npm:which@2.0.2","npm:word-wrap","npm:yallist@5.0.0","npm:yocto-queue"]},"@mobility-network/marketing:lint":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/marketing":["apps/marketing/.gitignore","apps/marketing/README.md","apps/marketing/eslint.config.mjs","apps/marketing/next.config.ts","apps/marketing/package.json","apps/marketing/postcss.config.mjs","apps/marketing/project.json","apps/marketing/public/file.svg","apps/marketing/public/globe.svg","apps/marketing/public/next.svg","apps/marketing/public/vercel.svg","apps/marketing/public/window.svg","apps/marketing/src/app/favicon.ico","apps/marketing/src/app/globals.css","apps/marketing/src/app/layout.tsx","apps/marketing/src/app/page.tsx","apps/marketing/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@alloc/quick-lru","npm:@ampproject/remapping","npm:@emnapi/core","npm:@emnapi/runtime","npm:@emnapi/wasi-threads","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@img/sharp-darwin-arm64","npm:@img/sharp-darwin-x64","npm:@img/sharp-libvips-darwin-arm64","npm:@img/sharp-libvips-darwin-x64","npm:@img/sharp-libvips-linux-arm","npm:@img/sharp-libvips-linux-arm64","npm:@img/sharp-libvips-linux-ppc64","npm:@img/sharp-libvips-linux-s390x","npm:@img/sharp-libvips-linux-x64","npm:@img/sharp-libvips-linuxmusl-arm64","npm:@img/sharp-libvips-linuxmusl-x64","npm:@img/sharp-linux-arm","npm:@img/sharp-linux-arm64","npm:@img/sharp-linux-s390x","npm:@img/sharp-linux-x64","npm:@img/sharp-linuxmusl-arm64","npm:@img/sharp-linuxmusl-x64","npm:@img/sharp-wasm32","npm:@img/sharp-win32-arm64","npm:@img/sharp-win32-ia32","npm:@img/sharp-win32-x64","npm:@isaacs/fs-minipass","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@napi-rs/wasm-runtime@0.2.11","npm:@next/env","npm:@next/eslint-plugin-next","npm:@next/swc-darwin-arm64","npm:@next/swc-darwin-x64","npm:@next/swc-linux-arm64-gnu","npm:@next/swc-linux-arm64-musl","npm:@next/swc-linux-x64-gnu","npm:@next/swc-linux-x64-musl","npm:@next/swc-win32-arm64-msvc","npm:@next/swc-win32-x64-msvc","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@nolyfill/is-core-module","npm:@rtsao/scc","npm:@rushstack/eslint-patch","npm:@swc/counter","npm:@swc/helpers","npm:@tailwindcss/node","npm:@tailwindcss/oxide","npm:@tailwindcss/oxide-android-arm64","npm:@tailwindcss/oxide-darwin-arm64","npm:@tailwindcss/oxide-darwin-x64","npm:@tailwindcss/oxide-freebsd-x64","npm:@tailwindcss/oxide-linux-arm-gnueabihf","npm:@tailwindcss/oxide-linux-arm64-gnu","npm:@tailwindcss/oxide-linux-arm64-musl","npm:@tailwindcss/oxide-linux-x64-gnu","npm:@tailwindcss/oxide-linux-x64-musl","npm:@tailwindcss/oxide-wasm32-wasi","npm:@tailwindcss/oxide-win32-arm64-msvc","npm:@tailwindcss/oxide-win32-x64-msvc","npm:@tailwindcss/postcss","npm:@tybys/wasm-util","npm:@types/estree","npm:@types/json-schema","npm:@types/json5","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@unrs/resolver-binding-android-arm-eabi","npm:@unrs/resolver-binding-android-arm64","npm:@unrs/resolver-binding-darwin-arm64","npm:@unrs/resolver-binding-darwin-x64","npm:@unrs/resolver-binding-freebsd-x64","npm:@unrs/resolver-binding-linux-arm-gnueabihf","npm:@unrs/resolver-binding-linux-arm-musleabihf","npm:@unrs/resolver-binding-linux-arm64-gnu","npm:@unrs/resolver-binding-linux-arm64-musl","npm:@unrs/resolver-binding-linux-ppc64-gnu","npm:@unrs/resolver-binding-linux-riscv64-gnu","npm:@unrs/resolver-binding-linux-riscv64-musl","npm:@unrs/resolver-binding-linux-s390x-gnu","npm:@unrs/resolver-binding-linux-x64-gnu","npm:@unrs/resolver-binding-linux-x64-musl","npm:@unrs/resolver-binding-wasm32-wasi","npm:@unrs/resolver-binding-win32-arm64-msvc","npm:@unrs/resolver-binding-win32-ia32-msvc","npm:@unrs/resolver-binding-win32-x64-msvc","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:aria-query","npm:array-buffer-byte-length","npm:array-includes","npm:array.prototype.findlast","npm:array.prototype.findlastindex","npm:array.prototype.flat","npm:array.prototype.flatmap","npm:array.prototype.tosorted","npm:arraybuffer.prototype.slice","npm:ast-types-flow","npm:async-function","npm:available-typed-arrays","npm:axe-core","npm:axobject-query","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:busboy","npm:call-bind","npm:call-bind-apply-helpers","npm:call-bound","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:chownr","npm:client-only","npm:color","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:color-string","npm:concat-map","npm:cross-spawn","npm:csstype","npm:damerau-levenshtein","npm:data-view-buffer","npm:data-view-byte-length","npm:data-view-byte-offset","npm:debug@3.2.7","npm:debug@4.4.1","npm:deep-is","npm:define-data-property","npm:define-properties","npm:detect-libc","npm:doctrine","npm:dunder-proto","npm:emoji-regex@9.2.2","npm:enhanced-resolve","npm:es-abstract","npm:es-define-property","npm:es-errors","npm:es-iterator-helpers","npm:es-object-atoms","npm:es-set-tostringtag","npm:es-shim-unscopables","npm:es-to-primitive","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-config-next","npm:eslint-import-context","npm:eslint-import-resolver-node","npm:eslint-import-resolver-typescript","npm:eslint-import-resolver-typescript@3.10.1","npm:eslint-module-utils","npm:eslint-plugin-import","npm:eslint-plugin-jsx-a11y","npm:eslint-plugin-react","npm:eslint-plugin-react-hooks","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.1","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:for-each","npm:function-bind","npm:function.prototype.name","npm:functions-have-names","npm:get-intrinsic","npm:get-proto","npm:get-symbol-description","npm:get-tsconfig","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals@14.0.0","npm:globalthis","npm:gopd","npm:graceful-fs","npm:graphemer","npm:has-bigints","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-property-descriptors","npm:has-proto","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:internal-slot","npm:is-array-buffer","npm:is-arrayish@0.3.2","npm:is-async-function","npm:is-bigint","npm:is-boolean-object","npm:is-bun-module","npm:is-callable","npm:is-core-module","npm:is-data-view","npm:is-date-object","npm:is-extglob","npm:is-finalizationregistry","npm:is-generator-function","npm:is-glob","npm:is-map","npm:is-negative-zero","npm:is-number","npm:is-number-object","npm:is-regex","npm:is-set","npm:is-shared-array-buffer","npm:is-string","npm:is-symbol","npm:is-typed-array","npm:is-weakmap","npm:is-weakref","npm:is-weakset","npm:isarray","npm:isexe","npm:iterator.prototype","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@1.0.2","npm:jsx-ast-utils","npm:keyv","npm:language-subtag-registry","npm:language-tags","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:loose-envify","npm:magic-string","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:minimist@1.2.8","npm:minipass","npm:minizlib","npm:mkdirp","npm:ms@2.1.3","npm:nanoid","npm:napi-postinstall","npm:natural-compare","npm:next","npm:object-assign","npm:object-inspect","npm:object-keys","npm:object.assign","npm:object.entries","npm:object.fromentries","npm:object.groupby","npm:object.values","npm:optionator","npm:own-keys","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:path-parse","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:possible-typed-array-names","npm:postcss@8.4.31","npm:postcss@8.5.6","npm:prelude-ls","npm:prop-types","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-is@16.13.1","npm:reflect.getprototypeof","npm:regexp.prototype.flags","npm:resolve-from@4.0.0","npm:resolve-pkg-maps","npm:resolve@1.22.10","npm:resolve@2.0.0-next.5","npm:reusify","npm:run-parallel","npm:safe-array-concat","npm:safe-push-apply","npm:safe-regex-test","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:set-function-length","npm:set-function-name","npm:set-proto","npm:sharp","npm:shebang-command","npm:shebang-regex","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-swizzle","npm:source-map-js","npm:stable-hash","npm:stable-hash-x","npm:stop-iteration-iterator","npm:streamsearch","npm:string.prototype.includes","npm:string.prototype.matchall","npm:string.prototype.repeat","npm:string.prototype.trim","npm:string.prototype.trimend","npm:string.prototype.trimstart","npm:strip-bom@3.0.0","npm:strip-json-comments","npm:styled-jsx","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:supports-preserve-symlinks-flag","npm:tailwindcss","npm:tapable","npm:tar","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:tsconfig-paths@3.15.0","npm:tslib","npm:type-check","npm:typed-array-buffer","npm:typed-array-byte-length","npm:typed-array-byte-offset","npm:typed-array-length","npm:typescript","npm:unbox-primitive","npm:undici-types@6.21.0","npm:unrs-resolver","npm:uri-js","npm:which-boxed-primitive","npm:which-builtin-type","npm:which-collection","npm:which-typed-array","npm:which@2.0.2","npm:word-wrap","npm:yallist@5.0.0","npm:yocto-queue"]},"@mobility-network/marketing:dev":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/marketing":["apps/marketing/.gitignore","apps/marketing/README.md","apps/marketing/eslint.config.mjs","apps/marketing/next.config.ts","apps/marketing/package.json","apps/marketing/postcss.config.mjs","apps/marketing/project.json","apps/marketing/public/file.svg","apps/marketing/public/globe.svg","apps/marketing/public/next.svg","apps/marketing/public/vercel.svg","apps/marketing/public/window.svg","apps/marketing/src/app/favicon.ico","apps/marketing/src/app/globals.css","apps/marketing/src/app/layout.tsx","apps/marketing/src/app/page.tsx","apps/marketing/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@alloc/quick-lru","npm:@ampproject/remapping","npm:@emnapi/core","npm:@emnapi/runtime","npm:@emnapi/wasi-threads","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@img/sharp-darwin-arm64","npm:@img/sharp-darwin-x64","npm:@img/sharp-libvips-darwin-arm64","npm:@img/sharp-libvips-darwin-x64","npm:@img/sharp-libvips-linux-arm","npm:@img/sharp-libvips-linux-arm64","npm:@img/sharp-libvips-linux-ppc64","npm:@img/sharp-libvips-linux-s390x","npm:@img/sharp-libvips-linux-x64","npm:@img/sharp-libvips-linuxmusl-arm64","npm:@img/sharp-libvips-linuxmusl-x64","npm:@img/sharp-linux-arm","npm:@img/sharp-linux-arm64","npm:@img/sharp-linux-s390x","npm:@img/sharp-linux-x64","npm:@img/sharp-linuxmusl-arm64","npm:@img/sharp-linuxmusl-x64","npm:@img/sharp-wasm32","npm:@img/sharp-win32-arm64","npm:@img/sharp-win32-ia32","npm:@img/sharp-win32-x64","npm:@isaacs/fs-minipass","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@napi-rs/wasm-runtime@0.2.11","npm:@next/env","npm:@next/eslint-plugin-next","npm:@next/swc-darwin-arm64","npm:@next/swc-darwin-x64","npm:@next/swc-linux-arm64-gnu","npm:@next/swc-linux-arm64-musl","npm:@next/swc-linux-x64-gnu","npm:@next/swc-linux-x64-musl","npm:@next/swc-win32-arm64-msvc","npm:@next/swc-win32-x64-msvc","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@nolyfill/is-core-module","npm:@rtsao/scc","npm:@rushstack/eslint-patch","npm:@swc/counter","npm:@swc/helpers","npm:@tailwindcss/node","npm:@tailwindcss/oxide","npm:@tailwindcss/oxide-android-arm64","npm:@tailwindcss/oxide-darwin-arm64","npm:@tailwindcss/oxide-darwin-x64","npm:@tailwindcss/oxide-freebsd-x64","npm:@tailwindcss/oxide-linux-arm-gnueabihf","npm:@tailwindcss/oxide-linux-arm64-gnu","npm:@tailwindcss/oxide-linux-arm64-musl","npm:@tailwindcss/oxide-linux-x64-gnu","npm:@tailwindcss/oxide-linux-x64-musl","npm:@tailwindcss/oxide-wasm32-wasi","npm:@tailwindcss/oxide-win32-arm64-msvc","npm:@tailwindcss/oxide-win32-x64-msvc","npm:@tailwindcss/postcss","npm:@tybys/wasm-util","npm:@types/estree","npm:@types/json-schema","npm:@types/json5","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@unrs/resolver-binding-android-arm-eabi","npm:@unrs/resolver-binding-android-arm64","npm:@unrs/resolver-binding-darwin-arm64","npm:@unrs/resolver-binding-darwin-x64","npm:@unrs/resolver-binding-freebsd-x64","npm:@unrs/resolver-binding-linux-arm-gnueabihf","npm:@unrs/resolver-binding-linux-arm-musleabihf","npm:@unrs/resolver-binding-linux-arm64-gnu","npm:@unrs/resolver-binding-linux-arm64-musl","npm:@unrs/resolver-binding-linux-ppc64-gnu","npm:@unrs/resolver-binding-linux-riscv64-gnu","npm:@unrs/resolver-binding-linux-riscv64-musl","npm:@unrs/resolver-binding-linux-s390x-gnu","npm:@unrs/resolver-binding-linux-x64-gnu","npm:@unrs/resolver-binding-linux-x64-musl","npm:@unrs/resolver-binding-wasm32-wasi","npm:@unrs/resolver-binding-win32-arm64-msvc","npm:@unrs/resolver-binding-win32-ia32-msvc","npm:@unrs/resolver-binding-win32-x64-msvc","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:aria-query","npm:array-buffer-byte-length","npm:array-includes","npm:array.prototype.findlast","npm:array.prototype.findlastindex","npm:array.prototype.flat","npm:array.prototype.flatmap","npm:array.prototype.tosorted","npm:arraybuffer.prototype.slice","npm:ast-types-flow","npm:async-function","npm:available-typed-arrays","npm:axe-core","npm:axobject-query","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:busboy","npm:call-bind","npm:call-bind-apply-helpers","npm:call-bound","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:chownr","npm:client-only","npm:color","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:color-string","npm:concat-map","npm:cross-spawn","npm:csstype","npm:damerau-levenshtein","npm:data-view-buffer","npm:data-view-byte-length","npm:data-view-byte-offset","npm:debug@3.2.7","npm:debug@4.4.1","npm:deep-is","npm:define-data-property","npm:define-properties","npm:detect-libc","npm:doctrine","npm:dunder-proto","npm:emoji-regex@9.2.2","npm:enhanced-resolve","npm:es-abstract","npm:es-define-property","npm:es-errors","npm:es-iterator-helpers","npm:es-object-atoms","npm:es-set-tostringtag","npm:es-shim-unscopables","npm:es-to-primitive","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-config-next","npm:eslint-import-context","npm:eslint-import-resolver-node","npm:eslint-import-resolver-typescript","npm:eslint-import-resolver-typescript@3.10.1","npm:eslint-module-utils","npm:eslint-plugin-import","npm:eslint-plugin-jsx-a11y","npm:eslint-plugin-react","npm:eslint-plugin-react-hooks","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.1","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:for-each","npm:function-bind","npm:function.prototype.name","npm:functions-have-names","npm:get-intrinsic","npm:get-proto","npm:get-symbol-description","npm:get-tsconfig","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals@14.0.0","npm:globalthis","npm:gopd","npm:graceful-fs","npm:graphemer","npm:has-bigints","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-property-descriptors","npm:has-proto","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:internal-slot","npm:is-array-buffer","npm:is-arrayish@0.3.2","npm:is-async-function","npm:is-bigint","npm:is-boolean-object","npm:is-bun-module","npm:is-callable","npm:is-core-module","npm:is-data-view","npm:is-date-object","npm:is-extglob","npm:is-finalizationregistry","npm:is-generator-function","npm:is-glob","npm:is-map","npm:is-negative-zero","npm:is-number","npm:is-number-object","npm:is-regex","npm:is-set","npm:is-shared-array-buffer","npm:is-string","npm:is-symbol","npm:is-typed-array","npm:is-weakmap","npm:is-weakref","npm:is-weakset","npm:isarray","npm:isexe","npm:iterator.prototype","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@1.0.2","npm:jsx-ast-utils","npm:keyv","npm:language-subtag-registry","npm:language-tags","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:loose-envify","npm:magic-string","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:minimist@1.2.8","npm:minipass","npm:minizlib","npm:mkdirp","npm:ms@2.1.3","npm:nanoid","npm:napi-postinstall","npm:natural-compare","npm:next","npm:object-assign","npm:object-inspect","npm:object-keys","npm:object.assign","npm:object.entries","npm:object.fromentries","npm:object.groupby","npm:object.values","npm:optionator","npm:own-keys","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:path-parse","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:possible-typed-array-names","npm:postcss@8.4.31","npm:postcss@8.5.6","npm:prelude-ls","npm:prop-types","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-is@16.13.1","npm:reflect.getprototypeof","npm:regexp.prototype.flags","npm:resolve-from@4.0.0","npm:resolve-pkg-maps","npm:resolve@1.22.10","npm:resolve@2.0.0-next.5","npm:reusify","npm:run-parallel","npm:safe-array-concat","npm:safe-push-apply","npm:safe-regex-test","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:set-function-length","npm:set-function-name","npm:set-proto","npm:sharp","npm:shebang-command","npm:shebang-regex","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-swizzle","npm:source-map-js","npm:stable-hash","npm:stable-hash-x","npm:stop-iteration-iterator","npm:streamsearch","npm:string.prototype.includes","npm:string.prototype.matchall","npm:string.prototype.repeat","npm:string.prototype.trim","npm:string.prototype.trimend","npm:string.prototype.trimstart","npm:strip-bom@3.0.0","npm:strip-json-comments","npm:styled-jsx","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:supports-preserve-symlinks-flag","npm:tailwindcss","npm:tapable","npm:tar","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:tsconfig-paths@3.15.0","npm:tslib","npm:type-check","npm:typed-array-buffer","npm:typed-array-byte-length","npm:typed-array-byte-offset","npm:typed-array-length","npm:typescript","npm:unbox-primitive","npm:undici-types@6.21.0","npm:unrs-resolver","npm:uri-js","npm:which-boxed-primitive","npm:which-builtin-type","npm:which-collection","npm:which-typed-array","npm:which@2.0.2","npm:word-wrap","npm:yallist@5.0.0","npm:yocto-queue"]},"@mobility-network/marketing:build":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/marketing":["apps/marketing/.gitignore","apps/marketing/README.md","apps/marketing/eslint.config.mjs","apps/marketing/next.config.ts","apps/marketing/package.json","apps/marketing/postcss.config.mjs","apps/marketing/project.json","apps/marketing/public/file.svg","apps/marketing/public/globe.svg","apps/marketing/public/next.svg","apps/marketing/public/vercel.svg","apps/marketing/public/window.svg","apps/marketing/src/app/favicon.ico","apps/marketing/src/app/globals.css","apps/marketing/src/app/layout.tsx","apps/marketing/src/app/page.tsx","apps/marketing/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@alloc/quick-lru","npm:@ampproject/remapping","npm:@emnapi/core","npm:@emnapi/runtime","npm:@emnapi/wasi-threads","npm:@eslint-community/eslint-utils","npm:@eslint-community/regexpp","npm:@eslint/config-array","npm:@eslint/config-helpers","npm:@eslint/core@0.14.0","npm:@eslint/core@0.15.0","npm:@eslint/eslintrc","npm:@eslint/js","npm:@eslint/object-schema","npm:@eslint/plugin-kit","npm:@humanfs/core","npm:@humanfs/node","npm:@humanwhocodes/module-importer","npm:@humanwhocodes/retry@0.3.1","npm:@humanwhocodes/retry@0.4.3","npm:@img/sharp-darwin-arm64","npm:@img/sharp-darwin-x64","npm:@img/sharp-libvips-darwin-arm64","npm:@img/sharp-libvips-darwin-x64","npm:@img/sharp-libvips-linux-arm","npm:@img/sharp-libvips-linux-arm64","npm:@img/sharp-libvips-linux-ppc64","npm:@img/sharp-libvips-linux-s390x","npm:@img/sharp-libvips-linux-x64","npm:@img/sharp-libvips-linuxmusl-arm64","npm:@img/sharp-libvips-linuxmusl-x64","npm:@img/sharp-linux-arm","npm:@img/sharp-linux-arm64","npm:@img/sharp-linux-s390x","npm:@img/sharp-linux-x64","npm:@img/sharp-linuxmusl-arm64","npm:@img/sharp-linuxmusl-x64","npm:@img/sharp-wasm32","npm:@img/sharp-win32-arm64","npm:@img/sharp-win32-ia32","npm:@img/sharp-win32-x64","npm:@isaacs/fs-minipass","npm:@jridgewell/gen-mapping","npm:@jridgewell/resolve-uri","npm:@jridgewell/set-array","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.25","npm:@napi-rs/wasm-runtime@0.2.11","npm:@next/env","npm:@next/eslint-plugin-next","npm:@next/swc-darwin-arm64","npm:@next/swc-darwin-x64","npm:@next/swc-linux-arm64-gnu","npm:@next/swc-linux-arm64-musl","npm:@next/swc-linux-x64-gnu","npm:@next/swc-linux-x64-musl","npm:@next/swc-win32-arm64-msvc","npm:@next/swc-win32-x64-msvc","npm:@nodelib/fs.scandir","npm:@nodelib/fs.stat","npm:@nodelib/fs.walk","npm:@nolyfill/is-core-module","npm:@rtsao/scc","npm:@rushstack/eslint-patch","npm:@swc/counter","npm:@swc/helpers","npm:@tailwindcss/node","npm:@tailwindcss/oxide","npm:@tailwindcss/oxide-android-arm64","npm:@tailwindcss/oxide-darwin-arm64","npm:@tailwindcss/oxide-darwin-x64","npm:@tailwindcss/oxide-freebsd-x64","npm:@tailwindcss/oxide-linux-arm-gnueabihf","npm:@tailwindcss/oxide-linux-arm64-gnu","npm:@tailwindcss/oxide-linux-arm64-musl","npm:@tailwindcss/oxide-linux-x64-gnu","npm:@tailwindcss/oxide-linux-x64-musl","npm:@tailwindcss/oxide-wasm32-wasi","npm:@tailwindcss/oxide-win32-arm64-msvc","npm:@tailwindcss/oxide-win32-x64-msvc","npm:@tailwindcss/postcss","npm:@tybys/wasm-util","npm:@types/estree","npm:@types/json-schema","npm:@types/json5","npm:@types/node@20.19.1","npm:@types/react","npm:@types/react-dom","npm:@typescript-eslint/eslint-plugin","npm:@typescript-eslint/parser","npm:@typescript-eslint/project-service","npm:@typescript-eslint/scope-manager","npm:@typescript-eslint/tsconfig-utils","npm:@typescript-eslint/type-utils","npm:@typescript-eslint/types","npm:@typescript-eslint/typescript-estree","npm:@typescript-eslint/utils","npm:@typescript-eslint/visitor-keys","npm:@unrs/resolver-binding-android-arm-eabi","npm:@unrs/resolver-binding-android-arm64","npm:@unrs/resolver-binding-darwin-arm64","npm:@unrs/resolver-binding-darwin-x64","npm:@unrs/resolver-binding-freebsd-x64","npm:@unrs/resolver-binding-linux-arm-gnueabihf","npm:@unrs/resolver-binding-linux-arm-musleabihf","npm:@unrs/resolver-binding-linux-arm64-gnu","npm:@unrs/resolver-binding-linux-arm64-musl","npm:@unrs/resolver-binding-linux-ppc64-gnu","npm:@unrs/resolver-binding-linux-riscv64-gnu","npm:@unrs/resolver-binding-linux-riscv64-musl","npm:@unrs/resolver-binding-linux-s390x-gnu","npm:@unrs/resolver-binding-linux-x64-gnu","npm:@unrs/resolver-binding-linux-x64-musl","npm:@unrs/resolver-binding-wasm32-wasi","npm:@unrs/resolver-binding-win32-arm64-msvc","npm:@unrs/resolver-binding-win32-ia32-msvc","npm:@unrs/resolver-binding-win32-x64-msvc","npm:acorn","npm:acorn-jsx","npm:ajv@6.12.6","npm:ansi-styles@4.3.0","npm:argparse@2.0.1","npm:aria-query","npm:array-buffer-byte-length","npm:array-includes","npm:array.prototype.findlast","npm:array.prototype.findlastindex","npm:array.prototype.flat","npm:array.prototype.flatmap","npm:array.prototype.tosorted","npm:arraybuffer.prototype.slice","npm:ast-types-flow","npm:async-function","npm:available-typed-arrays","npm:axe-core","npm:axobject-query","npm:balanced-match","npm:brace-expansion@1.1.12","npm:brace-expansion@2.0.2","npm:braces","npm:busboy","npm:call-bind","npm:call-bind-apply-helpers","npm:call-bound","npm:callsites","npm:caniuse-lite","npm:chalk@4.1.2","npm:chownr","npm:client-only","npm:color","npm:color-convert@2.0.1","npm:color-name@1.1.4","npm:color-string","npm:concat-map","npm:cross-spawn","npm:csstype","npm:damerau-levenshtein","npm:data-view-buffer","npm:data-view-byte-length","npm:data-view-byte-offset","npm:debug@3.2.7","npm:debug@4.4.1","npm:deep-is","npm:define-data-property","npm:define-properties","npm:detect-libc","npm:doctrine","npm:dunder-proto","npm:emoji-regex@9.2.2","npm:enhanced-resolve","npm:es-abstract","npm:es-define-property","npm:es-errors","npm:es-iterator-helpers","npm:es-object-atoms","npm:es-set-tostringtag","npm:es-shim-unscopables","npm:es-to-primitive","npm:escape-string-regexp@4.0.0","npm:eslint","npm:eslint-config-next","npm:eslint-import-context","npm:eslint-import-resolver-node","npm:eslint-import-resolver-typescript","npm:eslint-import-resolver-typescript@3.10.1","npm:eslint-module-utils","npm:eslint-plugin-import","npm:eslint-plugin-jsx-a11y","npm:eslint-plugin-react","npm:eslint-plugin-react-hooks","npm:eslint-scope","npm:eslint-visitor-keys@3.4.3","npm:eslint-visitor-keys@4.2.1","npm:espree","npm:esquery","npm:esrecurse","npm:estraverse","npm:esutils","npm:fast-deep-equal","npm:fast-glob@3.3.1","npm:fast-glob@3.3.3","npm:fast-json-stable-stringify","npm:fast-levenshtein","npm:fastq","npm:fdir","npm:file-entry-cache","npm:fill-range","npm:find-up","npm:flat-cache","npm:flatted","npm:for-each","npm:function-bind","npm:function.prototype.name","npm:functions-have-names","npm:get-intrinsic","npm:get-proto","npm:get-symbol-description","npm:get-tsconfig","npm:glob-parent@5.1.2","npm:glob-parent@6.0.2","npm:globals@14.0.0","npm:globalthis","npm:gopd","npm:graceful-fs","npm:graphemer","npm:has-bigints","npm:has-flag@3.0.0","npm:has-flag@4.0.0","npm:has-property-descriptors","npm:has-proto","npm:has-symbols","npm:has-tostringtag","npm:hasown","npm:ignore@5.3.2","npm:ignore@7.0.5","npm:import-fresh","npm:imurmurhash","npm:internal-slot","npm:is-array-buffer","npm:is-arrayish@0.3.2","npm:is-async-function","npm:is-bigint","npm:is-boolean-object","npm:is-bun-module","npm:is-callable","npm:is-core-module","npm:is-data-view","npm:is-date-object","npm:is-extglob","npm:is-finalizationregistry","npm:is-generator-function","npm:is-glob","npm:is-map","npm:is-negative-zero","npm:is-number","npm:is-number-object","npm:is-regex","npm:is-set","npm:is-shared-array-buffer","npm:is-string","npm:is-symbol","npm:is-typed-array","npm:is-weakmap","npm:is-weakref","npm:is-weakset","npm:isarray","npm:isexe","npm:iterator.prototype","npm:jiti","npm:js-tokens","npm:js-yaml@4.1.0","npm:json-buffer","npm:json-schema-traverse@0.4.1","npm:json-stable-stringify-without-jsonify","npm:json5@1.0.2","npm:jsx-ast-utils","npm:keyv","npm:language-subtag-registry","npm:language-tags","npm:levn","npm:lightningcss","npm:lightningcss-darwin-arm64","npm:lightningcss-darwin-x64","npm:lightningcss-freebsd-x64","npm:lightningcss-linux-arm-gnueabihf","npm:lightningcss-linux-arm64-gnu","npm:lightningcss-linux-arm64-musl","npm:lightningcss-linux-x64-gnu","npm:lightningcss-linux-x64-musl","npm:lightningcss-win32-arm64-msvc","npm:lightningcss-win32-x64-msvc","npm:locate-path","npm:lodash.merge","npm:loose-envify","npm:magic-string","npm:math-intrinsics","npm:merge2","npm:micromatch","npm:minimatch@3.1.2","npm:minimatch@9.0.5","npm:minimist@1.2.8","npm:minipass","npm:minizlib","npm:mkdirp","npm:ms@2.1.3","npm:nanoid","npm:napi-postinstall","npm:natural-compare","npm:next","npm:object-assign","npm:object-inspect","npm:object-keys","npm:object.assign","npm:object.entries","npm:object.fromentries","npm:object.groupby","npm:object.values","npm:optionator","npm:own-keys","npm:p-limit","npm:p-locate","npm:parent-module","npm:path-exists","npm:path-key","npm:path-parse","npm:picocolors","npm:picomatch@2.3.1","npm:picomatch@4.0.2","npm:possible-typed-array-names","npm:postcss@8.4.31","npm:postcss@8.5.6","npm:prelude-ls","npm:prop-types","npm:punycode","npm:queue-microtask","npm:react","npm:react-dom","npm:react-is@16.13.1","npm:reflect.getprototypeof","npm:regexp.prototype.flags","npm:resolve-from@4.0.0","npm:resolve-pkg-maps","npm:resolve@1.22.10","npm:resolve@2.0.0-next.5","npm:reusify","npm:run-parallel","npm:safe-array-concat","npm:safe-push-apply","npm:safe-regex-test","npm:scheduler","npm:semver@6.3.1","npm:semver@7.7.2","npm:set-function-length","npm:set-function-name","npm:set-proto","npm:sharp","npm:shebang-command","npm:shebang-regex","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-swizzle","npm:source-map-js","npm:stable-hash","npm:stable-hash-x","npm:stop-iteration-iterator","npm:streamsearch","npm:string.prototype.includes","npm:string.prototype.matchall","npm:string.prototype.repeat","npm:string.prototype.trim","npm:string.prototype.trimend","npm:string.prototype.trimstart","npm:strip-bom@3.0.0","npm:strip-json-comments","npm:styled-jsx","npm:supports-color@5.5.0","npm:supports-color@7.2.0","npm:supports-preserve-symlinks-flag","npm:tailwindcss","npm:tapable","npm:tar","npm:tinyglobby","npm:to-regex-range","npm:ts-api-utils","npm:tsconfig-paths@3.15.0","npm:tslib","npm:type-check","npm:typed-array-buffer","npm:typed-array-byte-length","npm:typed-array-byte-offset","npm:typed-array-length","npm:typescript","npm:unbox-primitive","npm:undici-types@6.21.0","npm:unrs-resolver","npm:uri-js","npm:which-boxed-primitive","npm:which-builtin-type","npm:which-collection","npm:which-typed-array","npm:which@2.0.2","npm:word-wrap","npm:yallist@5.0.0","npm:yocto-queue"]},"@mobility-network/ui:dev":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript"]},"@mobility-network/ui:clean":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript"]},"@mobility-network/ui:lint":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript"]},"@mobility-network/ui:type-check":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/ui":["packages/ui/package.json","packages/ui/project.json","packages/ui/src/Button.tsx","packages/ui/src/index.ts","packages/ui/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@types/react","npm:@types/react-dom","npm:csstype","npm:react","npm:react-dom","npm:scheduler","npm:typescript"]},"@mobility-network/api:start":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/api":["apps/api/index.ts","apps/api/package.json","apps/api/project.json","apps/api/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn"]},"@mobility-network/api:lint":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/api":["apps/api/index.ts","apps/api/package.json","apps/api/project.json","apps/api/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn"]},"@mobility-network/api:lint:fix":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/api":["apps/api/index.ts","apps/api/package.json","apps/api/project.json","apps/api/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn"]},"@mobility-network/api:db:generate":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/api":["apps/api/index.ts","apps/api/package.json","apps/api/project.json","apps/api/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn"]},"@mobility-network/api:db:migrate":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/api":["apps/api/index.ts","apps/api/package.json","apps/api/project.json","apps/api/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn"]},"@mobility-network/api:dev":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/api":["apps/api/index.ts","apps/api/package.json","apps/api/project.json","apps/api/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn"]},"@mobility-network/api:build":{"general":[".gitignore","nx.json","AllExternalDependencies"],"@mobility-network/shared":["packages/shared/package.json","packages/shared/project.json","packages/shared/src/index.ts","packages/shared/src/type.ts","packages/shared/tsconfig.json"],"@mobility-network/api":["apps/api/index.ts","apps/api/package.json","apps/api/project.json","apps/api/tsconfig.json"],"external":["env:NX_CLOUD_ENCRYPTION_KEY","npm:@cspotcode/source-map-support","npm:@jridgewell/resolve-uri","npm:@jridgewell/sourcemap-codec","npm:@jridgewell/trace-mapping@0.3.9","npm:@tsconfig/node10","npm:@tsconfig/node12","npm:@tsconfig/node14","npm:@tsconfig/node16","npm:@types/body-parser","npm:@types/connect","npm:@types/cors","npm:@types/express","npm:@types/express-serve-static-core","npm:@types/helmet","npm:@types/http-errors","npm:@types/mime","npm:@types/morgan","npm:@types/node@24.0.3","npm:@types/qs","npm:@types/range-parser","npm:@types/send","npm:@types/serve-static","npm:accepts","npm:acorn","npm:acorn-walk","npm:anymatch","npm:arg","npm:balanced-match","npm:basic-auth","npm:binary-extensions","npm:body-parser","npm:brace-expansion@1.1.12","npm:braces","npm:bytes","npm:call-bind-apply-helpers","npm:call-bound","npm:chokidar","npm:concat-map","npm:content-disposition","npm:content-type","npm:cookie","npm:cookie-signature","npm:cors","npm:create-require","npm:debug@2.6.9","npm:debug@4.4.1","npm:depd","npm:diff","npm:dunder-proto","npm:ee-first","npm:encodeurl","npm:es-define-property","npm:es-errors","npm:es-object-atoms","npm:escape-html","npm:etag","npm:express","npm:fill-range","npm:finalhandler","npm:forwarded","npm:fresh","npm:fsevents","npm:function-bind","npm:get-intrinsic","npm:get-proto","npm:glob-parent@5.1.2","npm:gopd","npm:has-flag@3.0.0","npm:has-symbols","npm:hasown","npm:helmet","npm:http-errors","npm:iconv-lite@0.6.3","npm:ignore-by-default","npm:inherits","npm:ipaddr.js","npm:is-binary-path","npm:is-extglob","npm:is-glob","npm:is-number","npm:is-promise","npm:make-error","npm:math-intrinsics","npm:media-typer","npm:merge-descriptors","npm:mime-db@1.54.0","npm:mime-types@3.0.1","npm:minimatch@3.1.2","npm:morgan","npm:ms@2.0.0","npm:ms@2.1.3","npm:negotiator","npm:nodemon","npm:normalize-path","npm:object-assign","npm:object-inspect","npm:on-finished@2.3.0","npm:on-finished@2.4.1","npm:on-headers","npm:once","npm:parseurl","npm:path-to-regexp","npm:picomatch@2.3.1","npm:proxy-addr","npm:pstree.remy","npm:qs","npm:range-parser","npm:raw-body","npm:readdirp","npm:router","npm:safe-buffer@5.1.2","npm:safe-buffer@5.2.1","npm:safer-buffer","npm:semver@7.7.2","npm:send","npm:serve-static","npm:setprototypeof","npm:side-channel","npm:side-channel-list","npm:side-channel-map","npm:side-channel-weakmap","npm:simple-update-notifier","npm:statuses@2.0.1","npm:statuses@2.0.2","npm:supports-color@5.5.0","npm:to-regex-range","npm:toidentifier","npm:touch","npm:ts-node","npm:type-is","npm:typescript","npm:undefsafe","npm:undici-types@7.8.0","npm:unpipe","npm:v8-compile-cache-lib","npm:vary","npm:wrappy","npm:yn"]}};window.sourceMapsResponse = {"apps/api":{"root":["apps/api/project.json","nx/core/project-json"],"name":["apps/api/project.json","nx/core/project-json"],"tags":["apps/api/package.json","nx/core/package-json"],"tags.npm:public":["apps/api/package.json","nx/core/package-json"],"metadata.targetGroups":["apps/api/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts":["apps/api/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.0":["apps/api/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.1":["apps/api/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.2":["apps/api/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.3":["apps/api/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.4":["apps/api/package.json","nx/core/package-json"],"metadata.description":["apps/api/package.json","nx/core/package-json"],"metadata.js":["apps/api/package.json","nx/core/package-json"],"metadata.js.packageName":["apps/api/package.json","nx/core/package-json"],"metadata.js.packageMain":["apps/api/package.json","nx/core/package-json"],"metadata.js.isInPackageManagerWorkspaces":["apps/api/package.json","nx/core/package-json"],"targets":["apps/api/package.json","nx/core/package-json"],"targets.start":["apps/api/package.json","nx/core/package-json"],"targets.start.executor":["apps/api/package.json","nx/core/package-json"],"targets.start.options":["apps/api/package.json","nx/core/package-json"],"targets.start.metadata":["apps/api/package.json","nx/core/package-json"],"targets.start.options.script":["apps/api/package.json","nx/core/package-json"],"targets.start.metadata.scriptContent":["apps/api/package.json","nx/core/package-json"],"targets.start.metadata.runCommand":["apps/api/package.json","nx/core/package-json"],"targets.lint":["apps/api/package.json","nx/core/package-json"],"targets.lint.executor":["apps/api/package.json","nx/core/package-json"],"targets.lint.options":["apps/api/package.json","nx/core/package-json"],"targets.lint.metadata":["apps/api/package.json","nx/core/package-json"],"targets.lint.options.script":["apps/api/package.json","nx/core/package-json"],"targets.lint.metadata.scriptContent":["apps/api/package.json","nx/core/package-json"],"targets.lint.metadata.runCommand":["apps/api/package.json","nx/core/package-json"],"targets.lint:fix":["apps/api/package.json","nx/core/package-json"],"targets.lint:fix.executor":["apps/api/package.json","nx/core/package-json"],"targets.lint:fix.options":["apps/api/package.json","nx/core/package-json"],"targets.lint:fix.metadata":["apps/api/package.json","nx/core/package-json"],"targets.lint:fix.options.script":["apps/api/package.json","nx/core/package-json"],"targets.lint:fix.metadata.scriptContent":["apps/api/package.json","nx/core/package-json"],"targets.lint:fix.metadata.runCommand":["apps/api/package.json","nx/core/package-json"],"targets.db:generate":["apps/api/package.json","nx/core/package-json"],"targets.db:generate.executor":["apps/api/package.json","nx/core/package-json"],"targets.db:generate.options":["apps/api/package.json","nx/core/package-json"],"targets.db:generate.metadata":["apps/api/package.json","nx/core/package-json"],"targets.db:generate.options.script":["apps/api/package.json","nx/core/package-json"],"targets.db:generate.metadata.scriptContent":["apps/api/package.json","nx/core/package-json"],"targets.db:generate.metadata.runCommand":["apps/api/package.json","nx/core/package-json"],"targets.db:migrate":["apps/api/package.json","nx/core/package-json"],"targets.db:migrate.executor":["apps/api/package.json","nx/core/package-json"],"targets.db:migrate.options":["apps/api/package.json","nx/core/package-json"],"targets.db:migrate.metadata":["apps/api/package.json","nx/core/package-json"],"targets.db:migrate.options.script":["apps/api/package.json","nx/core/package-json"],"targets.db:migrate.metadata.scriptContent":["apps/api/package.json","nx/core/package-json"],"targets.db:migrate.metadata.runCommand":["apps/api/package.json","nx/core/package-json"],"sourceRoot":["apps/api/project.json","nx/core/project-json"],"targets.dev":["apps/api/project.json","nx/core/project-json"],"targets.dev.executor":["apps/api/project.json","nx/core/project-json"],"targets.dev.options":["apps/api/project.json","nx/core/project-json"],"targets.dev.dependsOn":["apps/api/project.json","nx/core/project-json"],"targets.dev.options.command":["apps/api/project.json","nx/core/project-json"],"targets.dev.options.cwd":["apps/api/project.json","nx/core/project-json"],"targets.build":["apps/api/project.json","nx/core/project-json"],"targets.build.executor":["apps/api/project.json","nx/core/project-json"],"targets.build.options":["apps/api/project.json","nx/core/project-json"],"targets.build.dependsOn":["apps/api/project.json","nx/core/project-json"],"targets.build.options.command":["apps/api/project.json","nx/core/project-json"],"targets.build.options.cwd":["apps/api/project.json","nx/core/project-json"],"targets.dev.cache":["nx.json","nx/target-defaults"],"targets.dev.parallelism":["nx.json","nx/target-defaults"],"targets.build.cache":["nx.json","nx/target-defaults"],"targets.build.parallelism":["nx.json","nx/target-defaults"]},"apps/dashboard":{"root":["apps/dashboard/project.json","nx/core/project-json"],"name":["apps/dashboard/project.json","nx/core/project-json"],"tags":["apps/dashboard/package.json","nx/core/package-json"],"tags.npm:private":["apps/dashboard/package.json","nx/core/package-json"],"metadata.targetGroups":["apps/dashboard/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts":["apps/dashboard/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.0":["apps/dashboard/package.json","nx/core/package-json"],"metadata.js":["apps/dashboard/package.json","nx/core/package-json"],"metadata.js.packageName":["apps/dashboard/package.json","nx/core/package-json"],"metadata.js.isInPackageManagerWorkspaces":["apps/dashboard/package.json","nx/core/package-json"],"targets":["apps/dashboard/package.json","nx/core/package-json"],"targets.lint:fix":["apps/dashboard/package.json","nx/core/package-json"],"targets.lint:fix.executor":["apps/dashboard/package.json","nx/core/package-json"],"targets.lint:fix.options":["apps/dashboard/package.json","nx/core/package-json"],"targets.lint:fix.metadata":["apps/dashboard/package.json","nx/core/package-json"],"targets.lint:fix.options.script":["apps/dashboard/package.json","nx/core/package-json"],"targets.lint:fix.metadata.scriptContent":["apps/dashboard/package.json","nx/core/package-json"],"targets.lint:fix.metadata.runCommand":["apps/dashboard/package.json","nx/core/package-json"],"sourceRoot":["apps/dashboard/project.json","nx/core/project-json"],"projectType":["apps/dashboard/project.json","nx/core/project-json"],"tags.scope:dashboard":["apps/dashboard/project.json","nx/core/project-json"],"tags.type:app":["apps/dashboard/project.json","nx/core/project-json"],"targets.dev":["apps/dashboard/project.json","nx/core/project-json"],"targets.dev.executor":["apps/dashboard/project.json","nx/core/project-json"],"targets.dev.options":["apps/dashboard/project.json","nx/core/project-json"],"targets.dev.dependsOn":["apps/dashboard/project.json","nx/core/project-json"],"targets.dev.options.command":["apps/dashboard/project.json","nx/core/project-json"],"targets.dev.options.cwd":["apps/dashboard/project.json","nx/core/project-json"],"targets.build":["apps/dashboard/project.json","nx/core/project-json"],"targets.build.executor":["apps/dashboard/project.json","nx/core/project-json"],"targets.build.outputs":["apps/dashboard/project.json","nx/core/project-json"],"targets.build.options":["apps/dashboard/project.json","nx/core/project-json"],"targets.build.dependsOn":["apps/dashboard/project.json","nx/core/project-json"],"targets.build.options.command":["apps/dashboard/project.json","nx/core/project-json"],"targets.build.options.cwd":["apps/dashboard/project.json","nx/core/project-json"],"targets.preview":["apps/dashboard/project.json","nx/core/project-json"],"targets.preview.executor":["apps/dashboard/project.json","nx/core/project-json"],"targets.preview.options":["apps/dashboard/project.json","nx/core/project-json"],"targets.preview.dependsOn":["apps/dashboard/project.json","nx/core/project-json"],"targets.preview.options.command":["apps/dashboard/project.json","nx/core/project-json"],"targets.preview.options.cwd":["apps/dashboard/project.json","nx/core/project-json"],"targets.lint":["apps/dashboard/project.json","nx/core/project-json"],"targets.lint.executor":["apps/dashboard/project.json","nx/core/project-json"],"targets.lint.options":["apps/dashboard/project.json","nx/core/project-json"],"targets.lint.options.command":["apps/dashboard/project.json","nx/core/project-json"],"targets.lint.options.cwd":["apps/dashboard/project.json","nx/core/project-json"],"targets.dev.cache":["nx.json","nx/target-defaults"],"targets.dev.parallelism":["nx.json","nx/target-defaults"],"targets.build.cache":["nx.json","nx/target-defaults"],"targets.build.parallelism":["nx.json","nx/target-defaults"]},"apps/marketing":{"root":["apps/marketing/project.json","nx/core/project-json"],"name":["apps/marketing/project.json","nx/core/project-json"],"tags":["apps/marketing/package.json","nx/core/package-json"],"tags.npm:private":["apps/marketing/package.json","nx/core/package-json"],"metadata.targetGroups":["apps/marketing/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts":["apps/marketing/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.0":["apps/marketing/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.1":["apps/marketing/package.json","nx/core/package-json"],"metadata.js":["apps/marketing/package.json","nx/core/package-json"],"metadata.js.packageName":["apps/marketing/package.json","nx/core/package-json"],"metadata.js.isInPackageManagerWorkspaces":["apps/marketing/package.json","nx/core/package-json"],"targets":["apps/marketing/package.json","nx/core/package-json"],"targets.start":["apps/marketing/package.json","nx/core/package-json"],"targets.start.executor":["apps/marketing/package.json","nx/core/package-json"],"targets.start.options":["apps/marketing/package.json","nx/core/package-json"],"targets.start.metadata":["apps/marketing/package.json","nx/core/package-json"],"targets.start.options.script":["apps/marketing/package.json","nx/core/package-json"],"targets.start.metadata.scriptContent":["apps/marketing/package.json","nx/core/package-json"],"targets.start.metadata.runCommand":["apps/marketing/package.json","nx/core/package-json"],"targets.lint":["apps/marketing/package.json","nx/core/package-json"],"targets.lint.executor":["apps/marketing/package.json","nx/core/package-json"],"targets.lint.options":["apps/marketing/package.json","nx/core/package-json"],"targets.lint.metadata":["apps/marketing/package.json","nx/core/package-json"],"targets.lint.options.script":["apps/marketing/package.json","nx/core/package-json"],"targets.lint.metadata.scriptContent":["apps/marketing/package.json","nx/core/package-json"],"targets.lint.metadata.runCommand":["apps/marketing/package.json","nx/core/package-json"],"sourceRoot":["apps/marketing/project.json","nx/core/project-json"],"targets.dev":["apps/marketing/project.json","nx/core/project-json"],"targets.dev.executor":["apps/marketing/project.json","nx/core/project-json"],"targets.dev.options":["apps/marketing/project.json","nx/core/project-json"],"targets.dev.dependsOn":["apps/marketing/project.json","nx/core/project-json"],"targets.dev.options.command":["apps/marketing/project.json","nx/core/project-json"],"targets.dev.options.cwd":["apps/marketing/project.json","nx/core/project-json"],"targets.build":["apps/marketing/project.json","nx/core/project-json"],"targets.build.executor":["apps/marketing/project.json","nx/core/project-json"],"targets.build.options":["apps/marketing/project.json","nx/core/project-json"],"targets.build.dependsOn":["apps/marketing/project.json","nx/core/project-json"],"targets.build.options.command":["apps/marketing/project.json","nx/core/project-json"],"targets.build.options.cwd":["apps/marketing/project.json","nx/core/project-json"],"targets.dev.cache":["nx.json","nx/target-defaults"],"targets.dev.parallelism":["nx.json","nx/target-defaults"],"targets.build.cache":["nx.json","nx/target-defaults"],"targets.build.parallelism":["nx.json","nx/target-defaults"]},"packages/shared":{"root":["packages/shared/project.json","nx/core/project-json"],"name":["packages/shared/project.json","nx/core/project-json"],"tags":["packages/shared/package.json","nx/core/package-json"],"tags.npm:public":["packages/shared/package.json","nx/core/package-json"],"metadata.targetGroups":["packages/shared/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts":["packages/shared/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.0":["packages/shared/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.1":["packages/shared/package.json","nx/core/package-json"],"metadata.description":["packages/shared/package.json","nx/core/package-json"],"metadata.js":["packages/shared/package.json","nx/core/package-json"],"metadata.js.packageName":["packages/shared/package.json","nx/core/package-json"],"metadata.js.packageMain":["packages/shared/package.json","nx/core/package-json"],"metadata.js.isInPackageManagerWorkspaces":["packages/shared/package.json","nx/core/package-json"],"targets":["packages/shared/package.json","nx/core/package-json"],"targets.dev":["packages/shared/package.json","nx/core/package-json"],"targets.dev.executor":["packages/shared/package.json","nx/core/package-json"],"targets.dev.options":["packages/shared/package.json","nx/core/package-json"],"targets.dev.metadata":["packages/shared/package.json","nx/core/package-json"],"targets.dev.options.script":["packages/shared/package.json","nx/core/package-json"],"targets.dev.metadata.scriptContent":["packages/shared/package.json","nx/core/package-json"],"targets.dev.metadata.runCommand":["packages/shared/package.json","nx/core/package-json"],"targets.clean":["packages/shared/package.json","nx/core/package-json"],"targets.clean.executor":["packages/shared/package.json","nx/core/package-json"],"targets.clean.options":["packages/shared/package.json","nx/core/package-json"],"targets.clean.metadata":["packages/shared/package.json","nx/core/package-json"],"targets.clean.options.script":["packages/shared/package.json","nx/core/package-json"],"targets.clean.metadata.scriptContent":["packages/shared/package.json","nx/core/package-json"],"targets.clean.metadata.runCommand":["packages/shared/package.json","nx/core/package-json"],"sourceRoot":["packages/shared/project.json","nx/core/project-json"],"projectType":["packages/shared/project.json","nx/core/project-json"],"tags.scope:shared":["packages/shared/project.json","nx/core/project-json"],"tags.type:util":["packages/shared/project.json","nx/core/project-json"],"targets.build":["packages/shared/project.json","nx/core/project-json"],"targets.build.executor":["packages/shared/project.json","nx/core/project-json"],"targets.build.outputs":["packages/shared/project.json","nx/core/project-json"],"targets.build.options":["packages/shared/project.json","nx/core/project-json"],"targets.build.cache":["packages/shared/project.json","nx/core/project-json"],"targets.build.options.command":["packages/shared/project.json","nx/core/project-json"],"targets.build.options.cwd":["packages/shared/project.json","nx/core/project-json"],"targets.lint":["packages/shared/project.json","nx/core/project-json"],"targets.lint.executor":["packages/shared/project.json","nx/core/project-json"],"targets.lint.options":["packages/shared/project.json","nx/core/project-json"],"targets.lint.options.command":["packages/shared/project.json","nx/core/project-json"],"targets.lint.options.cwd":["packages/shared/project.json","nx/core/project-json"],"targets.type-check":["packages/shared/project.json","nx/core/project-json"],"targets.type-check.executor":["packages/shared/project.json","nx/core/project-json"],"targets.type-check.options":["packages/shared/project.json","nx/core/project-json"],"targets.type-check.options.command":["packages/shared/project.json","nx/core/project-json"],"targets.type-check.options.cwd":["packages/shared/project.json","nx/core/project-json"],"targets.dev.cache":["nx.json","nx/target-defaults"],"targets.dev.parallelism":["nx.json","nx/target-defaults"],"targets.build.dependsOn":["nx.json","nx/target-defaults"],"targets.build.parallelism":["nx.json","nx/target-defaults"]},"packages/ui":{"root":["packages/ui/project.json","nx/core/project-json"],"name":["packages/ui/project.json","nx/core/project-json"],"tags":["packages/ui/package.json","nx/core/package-json"],"tags.npm:public":["packages/ui/package.json","nx/core/package-json"],"metadata.targetGroups":["packages/ui/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts":["packages/ui/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.0":["packages/ui/package.json","nx/core/package-json"],"metadata.targetGroups.NPM Scripts.1":["packages/ui/package.json","nx/core/package-json"],"metadata.description":["packages/ui/package.json","nx/core/package-json"],"metadata.js":["packages/ui/package.json","nx/core/package-json"],"metadata.js.packageName":["packages/ui/package.json","nx/core/package-json"],"metadata.js.packageMain":["packages/ui/package.json","nx/core/package-json"],"metadata.js.isInPackageManagerWorkspaces":["packages/ui/package.json","nx/core/package-json"],"targets":["packages/ui/package.json","nx/core/package-json"],"targets.dev":["packages/ui/package.json","nx/core/package-json"],"targets.dev.executor":["packages/ui/package.json","nx/core/package-json"],"targets.dev.options":["packages/ui/package.json","nx/core/package-json"],"targets.dev.metadata":["packages/ui/package.json","nx/core/package-json"],"targets.dev.options.script":["packages/ui/package.json","nx/core/package-json"],"targets.dev.metadata.scriptContent":["packages/ui/package.json","nx/core/package-json"],"targets.dev.metadata.runCommand":["packages/ui/package.json","nx/core/package-json"],"targets.clean":["packages/ui/package.json","nx/core/package-json"],"targets.clean.executor":["packages/ui/package.json","nx/core/package-json"],"targets.clean.options":["packages/ui/package.json","nx/core/package-json"],"targets.clean.metadata":["packages/ui/package.json","nx/core/package-json"],"targets.clean.options.script":["packages/ui/package.json","nx/core/package-json"],"targets.clean.metadata.scriptContent":["packages/ui/package.json","nx/core/package-json"],"targets.clean.metadata.runCommand":["packages/ui/package.json","nx/core/package-json"],"sourceRoot":["packages/ui/project.json","nx/core/project-json"],"projectType":["packages/ui/project.json","nx/core/project-json"],"tags.scope:shared":["packages/ui/project.json","nx/core/project-json"],"tags.type:ui":["packages/ui/project.json","nx/core/project-json"],"targets.build":["packages/ui/project.json","nx/core/project-json"],"targets.build.executor":["packages/ui/project.json","nx/core/project-json"],"targets.build.outputs":["packages/ui/project.json","nx/core/project-json"],"targets.build.options":["packages/ui/project.json","nx/core/project-json"],"targets.build.cache":["packages/ui/project.json","nx/core/project-json"],"targets.build.options.command":["packages/ui/project.json","nx/core/project-json"],"targets.build.options.cwd":["packages/ui/project.json","nx/core/project-json"],"targets.lint":["packages/ui/project.json","nx/core/project-json"],"targets.lint.executor":["packages/ui/project.json","nx/core/project-json"],"targets.lint.options":["packages/ui/project.json","nx/core/project-json"],"targets.lint.options.command":["packages/ui/project.json","nx/core/project-json"],"targets.lint.options.cwd":["packages/ui/project.json","nx/core/project-json"],"targets.type-check":["packages/ui/project.json","nx/core/project-json"],"targets.type-check.executor":["packages/ui/project.json","nx/core/project-json"],"targets.type-check.options":["packages/ui/project.json","nx/core/project-json"],"targets.type-check.options.command":["packages/ui/project.json","nx/core/project-json"],"targets.type-check.options.cwd":["packages/ui/project.json","nx/core/project-json"],"targets.dev.cache":["nx.json","nx/target-defaults"],"targets.dev.parallelism":["nx.json","nx/target-defaults"],"targets.build.dependsOn":["nx.json","nx/target-defaults"],"targets.build.parallelism":["nx.json","nx/target-defaults"]}};